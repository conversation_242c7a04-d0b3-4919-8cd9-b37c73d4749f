{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space, Collapse } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { DashboardOutlined, ProjectOutlined, UserOutlined, TeamOutlined, BookOutlined, FileTextOutlined, GlobalOutlined, ThunderboltOutlined, ClockCircleOutlined, ShareAltOutlined, RobotOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined, LogoutOutlined, BellOutlined, EnvironmentOutlined, EyeOutlined, ShoppingOutlined, HeartOutlined, CompassOutlined, StarOutlined, CrownOutlined, BankOutlined, DownOutlined, RightOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst {\n  Panel\n} = Collapse;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [contentCollapsed, setContentCollapsed] = useState(false);\n  const [settingsCollapsed, setSettingsCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n  const projectId = getProjectId();\n\n  // 基础菜单项\n  const baseMenuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: '/projects',\n    icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    label: '项目管理'\n  }];\n\n  // 内容管理菜单项\n  const contentMenuItems = projectId ? [{\n    key: `/projects/${projectId}/characters`,\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this),\n    label: '人物管理'\n  }, {\n    key: `/projects/${projectId}/factions`,\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this),\n    label: '势力管理'\n  }, {\n    key: `/projects/${projectId}/plots`,\n    icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this),\n    label: '剧情管理'\n  }, {\n    key: `/projects/${projectId}/volumes`,\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this),\n    label: '卷宗管理'\n  }, {\n    key: `/projects/${projectId}/resource-distribution`,\n    icon: /*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this),\n    label: '资源分布'\n  }, {\n    key: `/projects/${projectId}/race-distribution`,\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this),\n    label: '种族分布'\n  }, {\n    key: `/projects/${projectId}/secret-realms`,\n    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this),\n    label: '秘境分布'\n  }] : [];\n\n  // 设定管理菜单项\n  const settingsMenuItems = projectId ? [{\n    key: `/projects/${projectId}/world-settings`,\n    icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 13\n    }, this),\n    label: '世界设定'\n  }, {\n    key: `/projects/${projectId}/cultivation-systems`,\n    icon: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 13\n    }, this),\n    label: '修炼体系'\n  }, {\n    key: `/projects/${projectId}/equipment-systems`,\n    icon: /*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 13\n    }, this),\n    label: '装备体系'\n  }, {\n    key: `/projects/${projectId}/pet-systems`,\n    icon: /*#__PURE__*/_jsxDEV(HeartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this),\n    label: '宠物体系'\n  }, {\n    key: `/projects/${projectId}/map-structure`,\n    icon: /*#__PURE__*/_jsxDEV(CompassOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this),\n    label: '地图结构'\n  }, {\n    key: `/projects/${projectId}/dimension-structure`,\n    icon: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 13\n    }, this),\n    label: '维度结构'\n  }, {\n    key: `/projects/${projectId}/spiritual-treasure-systems`,\n    icon: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 13\n    }, this),\n    label: '灵宝体系'\n  }, {\n    key: `/projects/${projectId}/civilian-systems`,\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 13\n    }, this),\n    label: '生民体系'\n  }, {\n    key: `/projects/${projectId}/judicial-systems`,\n    icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 13\n    }, this),\n    label: '司法体系'\n  }, {\n    key: `/projects/${projectId}/profession-systems`,\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 13\n    }, this),\n    label: '职业体系'\n  }, {\n    key: `/projects/${projectId}/timeline`,\n    icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 13\n    }, this),\n    label: '时间线'\n  }, {\n    key: `/projects/${projectId}/relations`,\n    icon: /*#__PURE__*/_jsxDEV(ShareAltOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 13\n    }, this),\n    label: '关系网络'\n  }] : [];\n\n  // 工具菜单项\n  const toolsMenuItems = [{\n    key: '/ai-assistant',\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this),\n    label: 'AI助手'\n  }, {\n    key: '/ai-test',\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 13\n    }, this),\n    label: 'AI测试'\n  }, {\n    key: '/settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 13\n    }, this),\n    label: '系统设置'\n  }];\n\n  // 用户菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 13\n    }, this),\n    label: '偏好设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 13\n    }, this),\n    label: '退出登录'\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n  const handleCollapseMenuClick = key => {\n    navigate(key);\n  };\n  const handleUserMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    className: \"layout-container\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"layout-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              marginRight: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1890ff',\n              fontSize: '20px',\n              fontWeight: 'bold'\n            },\n            children: \"NovelCraft\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems,\n              onClick: handleUserMenuClick\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u7528\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      className: \"layout-content\",\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        className: \"layout-sider\",\n        collapsed: collapsed,\n        width: 240,\n        collapsedWidth: 80,\n        theme: \"light\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '100%',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"inline\",\n            selectedKeys: [location.pathname],\n            items: baseMenuItems,\n            onClick: handleMenuClick,\n            style: {\n              borderRight: 0,\n              marginBottom: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), projectId && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Collapse, {\n              ghost: true,\n              size: \"small\",\n              activeKey: contentCollapsed ? [] : ['content'],\n              onChange: keys => setContentCollapsed(!keys.includes('content')),\n              style: {\n                borderRight: 0\n              },\n              children: /*#__PURE__*/_jsxDEV(Panel, {\n                header: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    padding: '8px 0'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(BookOutlined, {\n                    style: {\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: 500\n                    },\n                    children: \"\\u5185\\u5BB9\\u7BA1\\u7406\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 23\n                }, this),\n                showArrow: !collapsed,\n                children: /*#__PURE__*/_jsxDEV(Menu, {\n                  mode: \"inline\",\n                  selectedKeys: [location.pathname],\n                  items: contentMenuItems,\n                  onClick: handleMenuClick,\n                  style: {\n                    borderRight: 0,\n                    backgroundColor: 'transparent'\n                  },\n                  inlineIndent: collapsed ? 12 : 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this)\n              }, \"content\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n              ghost: true,\n              size: \"small\",\n              activeKey: settingsCollapsed ? [] : ['settings'],\n              onChange: keys => setSettingsCollapsed(!keys.includes('settings')),\n              style: {\n                borderRight: 0\n              },\n              children: /*#__PURE__*/_jsxDEV(Panel, {\n                header: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    padding: '8px 0'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {\n                    style: {\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: 500\n                    },\n                    children: \"\\u8BBE\\u5B9A\\u7BA1\\u7406\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 23\n                }, this),\n                showArrow: !collapsed,\n                children: /*#__PURE__*/_jsxDEV(Menu, {\n                  mode: \"inline\",\n                  selectedKeys: [location.pathname],\n                  items: settingsMenuItems,\n                  onClick: handleMenuClick,\n                  style: {\n                    borderRight: 0,\n                    backgroundColor: 'transparent'\n                  },\n                  inlineIndent: collapsed ? 12 : 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this)\n              }, \"settings\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tools-divider\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tools-title\",\n              children: \"\\u5DE5\\u5177\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Menu, {\n              mode: \"inline\",\n              selectedKeys: [location.pathname],\n              items: toolsMenuItems,\n              onClick: handleMenuClick,\n              style: {\n                borderRight: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"layout-main\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"t/KUUEFbq3mghKN9ZxnFsjygAfg=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "Space", "Collapse", "useNavigate", "useLocation", "DashboardOutlined", "ProjectOutlined", "UserOutlined", "TeamOutlined", "BookOutlined", "FileTextOutlined", "GlobalOutlined", "ThunderboltOutlined", "ClockCircleOutlined", "ShareAltOutlined", "RobotOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "LogoutOutlined", "BellOutlined", "EnvironmentOutlined", "EyeOutlined", "ShoppingOutlined", "HeartOutlined", "CompassOutlined", "StarOutlined", "CrownOutlined", "BankOutlined", "DownOutlined", "RightOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "<PERSON><PERSON>", "Content", "Panel", "children", "_s", "collapsed", "setCollapsed", "contentCollapsed", "setContentCollapsed", "settingsCollapsed", "setSettingsCollapsed", "navigate", "location", "getProjectId", "pathParts", "pathname", "split", "projectId", "baseMenuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "contentMenuItems", "settingsMenuItems", "toolsMenuItems", "userMenuItems", "type", "handleMenuClick", "handleCollapseMenuClick", "handleUserMenuClick", "console", "log", "className", "style", "display", "alignItems", "justifyContent", "onClick", "marginRight", "margin", "color", "fontSize", "fontWeight", "menu", "items", "placement", "cursor", "width", "collapsedWidth", "theme", "height", "overflowY", "mode", "<PERSON><PERSON><PERSON><PERSON>", "borderRight", "marginBottom", "ghost", "size", "active<PERSON><PERSON>", "onChange", "keys", "includes", "header", "padding", "showArrow", "backgroundColor", "inlineIndent", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space, Collapse } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  DashboardOutlined,\n  ProjectOutlined,\n  UserOutlined,\n  TeamOutlined,\n  BookOutlined,\n  FileTextOutlined,\n  GlobalOutlined,\n  ThunderboltOutlined,\n  ClockCircleOutlined,\n  ShareAltOutlined,\n  RobotOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  EnvironmentOutlined,\n  EyeOutlined,\n  ShoppingOutlined,\n  HeartOutlined,\n  CompassOutlined,\n  StarOutlined,\n  CrownOutlined,\n  BankOutlined,\n  DownOutlined,\n  RightOutlined\n} from '@ant-design/icons';\n\nconst { Header, Sider, Content } = AntLayout;\nconst { Panel } = Collapse;\n\nconst Layout = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [contentCollapsed, setContentCollapsed] = useState(false);\n  const [settingsCollapsed, setSettingsCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n\n  const projectId = getProjectId();\n\n  // 基础菜单项\n  const baseMenuItems = [\n    {\n      key: '/',\n      icon: <DashboardOutlined />,\n      label: '仪表盘',\n    },\n    {\n      key: '/projects',\n      icon: <ProjectOutlined />,\n      label: '项目管理',\n    },\n  ];\n\n  // 内容管理菜单项\n  const contentMenuItems = projectId ? [\n    {\n      key: `/projects/${projectId}/characters`,\n      icon: <UserOutlined />,\n      label: '人物管理',\n    },\n    {\n      key: `/projects/${projectId}/factions`,\n      icon: <TeamOutlined />,\n      label: '势力管理',\n    },\n    {\n      key: `/projects/${projectId}/plots`,\n      icon: <BookOutlined />,\n      label: '剧情管理',\n    },\n    {\n      key: `/projects/${projectId}/volumes`,\n      icon: <FileTextOutlined />,\n      label: '卷宗管理',\n    },\n    {\n      key: `/projects/${projectId}/resource-distribution`,\n      icon: <EnvironmentOutlined />,\n      label: '资源分布',\n    },\n    {\n      key: `/projects/${projectId}/race-distribution`,\n      icon: <TeamOutlined />,\n      label: '种族分布',\n    },\n    {\n      key: `/projects/${projectId}/secret-realms`,\n      icon: <EyeOutlined />,\n      label: '秘境分布',\n    },\n  ] : [];\n\n  // 设定管理菜单项\n  const settingsMenuItems = projectId ? [\n    {\n      key: `/projects/${projectId}/world-settings`,\n      icon: <GlobalOutlined />,\n      label: '世界设定',\n    },\n    {\n      key: `/projects/${projectId}/cultivation-systems`,\n      icon: <ThunderboltOutlined />,\n      label: '修炼体系',\n    },\n    {\n      key: `/projects/${projectId}/equipment-systems`,\n      icon: <ShoppingOutlined />,\n      label: '装备体系',\n    },\n    {\n      key: `/projects/${projectId}/pet-systems`,\n      icon: <HeartOutlined />,\n      label: '宠物体系',\n    },\n    {\n      key: `/projects/${projectId}/map-structure`,\n      icon: <CompassOutlined />,\n      label: '地图结构',\n    },\n    {\n      key: `/projects/${projectId}/dimension-structure`,\n      icon: <StarOutlined />,\n      label: '维度结构',\n    },\n    {\n      key: `/projects/${projectId}/spiritual-treasure-systems`,\n      icon: <CrownOutlined />,\n      label: '灵宝体系',\n    },\n    {\n      key: `/projects/${projectId}/civilian-systems`,\n      icon: <TeamOutlined />,\n      label: '生民体系',\n    },\n    {\n      key: `/projects/${projectId}/judicial-systems`,\n      icon: <BankOutlined />,\n      label: '司法体系',\n    },\n    {\n      key: `/projects/${projectId}/profession-systems`,\n      icon: <UserOutlined />,\n      label: '职业体系',\n    },\n    {\n      key: `/projects/${projectId}/timeline`,\n      icon: <ClockCircleOutlined />,\n      label: '时间线',\n    },\n    {\n      key: `/projects/${projectId}/relations`,\n      icon: <ShareAltOutlined />,\n      label: '关系网络',\n    },\n  ] : [];\n\n  // 工具菜单项\n  const toolsMenuItems = [\n    {\n      key: '/ai-assistant',\n      icon: <RobotOutlined />,\n      label: 'AI助手',\n    },\n    {\n      key: '/ai-test',\n      icon: <RobotOutlined />,\n      label: 'AI测试',\n    },\n    {\n      key: '/settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '偏好设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    navigate(key);\n  };\n\n  const handleCollapseMenuClick = (key) => {\n    navigate(key);\n  };\n\n  const handleUserMenuClick = ({ key }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n\n  return (\n    <AntLayout className=\"layout-container\">\n      <Header className=\"layout-header\">\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{ marginRight: 16 }}\n            />\n            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>\n              NovelCraft\n            </h1>\n          </div>\n\n          <Space>\n            <Button type=\"text\" icon={<BellOutlined />} />\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>用户</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </div>\n      </Header>\n\n      <AntLayout className=\"layout-content\">\n        <Sider\n          className=\"layout-sider\"\n          collapsed={collapsed}\n          width={240}\n          collapsedWidth={80}\n          theme=\"light\"\n        >\n          <div style={{ height: '100%', overflowY: 'auto' }}>\n            {/* 基础菜单 */}\n            <Menu\n              mode=\"inline\"\n              selectedKeys={[location.pathname]}\n              items={baseMenuItems}\n              onClick={handleMenuClick}\n              style={{ borderRight: 0, marginBottom: 0 }}\n            />\n\n            {/* 项目相关菜单 */}\n            {projectId && (\n              <>\n                {/* 内容管理 */}\n                <Collapse\n                  ghost\n                  size=\"small\"\n                  activeKey={contentCollapsed ? [] : ['content']}\n                  onChange={(keys) => setContentCollapsed(!keys.includes('content'))}\n                  style={{ borderRight: 0 }}\n                >\n                  <Panel\n                    header={\n                      <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>\n                        <BookOutlined style={{ marginRight: 8 }} />\n                        <span style={{ fontWeight: 500 }}>内容管理</span>\n                      </div>\n                    }\n                    key=\"content\"\n                    showArrow={!collapsed}\n                  >\n                    <Menu\n                      mode=\"inline\"\n                      selectedKeys={[location.pathname]}\n                      items={contentMenuItems}\n                      onClick={handleMenuClick}\n                      style={{ borderRight: 0, backgroundColor: 'transparent' }}\n                      inlineIndent={collapsed ? 12 : 24}\n                    />\n                  </Panel>\n                </Collapse>\n\n                {/* 设定管理 */}\n                <Collapse\n                  ghost\n                  size=\"small\"\n                  activeKey={settingsCollapsed ? [] : ['settings']}\n                  onChange={(keys) => setSettingsCollapsed(!keys.includes('settings'))}\n                  style={{ borderRight: 0 }}\n                >\n                  <Panel\n                    header={\n                      <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>\n                        <SettingOutlined style={{ marginRight: 8 }} />\n                        <span style={{ fontWeight: 500 }}>设定管理</span>\n                      </div>\n                    }\n                    key=\"settings\"\n                    showArrow={!collapsed}\n                  >\n                    <Menu\n                      mode=\"inline\"\n                      selectedKeys={[location.pathname]}\n                      items={settingsMenuItems}\n                      onClick={handleMenuClick}\n                      style={{ borderRight: 0, backgroundColor: 'transparent' }}\n                      inlineIndent={collapsed ? 12 : 24}\n                    />\n                  </Panel>\n                </Collapse>\n              </>\n            )}\n\n            {/* 工具菜单 */}\n            <div className=\"tools-divider\">\n              <div className=\"tools-title\">\n                工具\n              </div>\n              <Menu\n                mode=\"inline\"\n                selectedKeys={[location.pathname]}\n                items={toolsMenuItems}\n                onClick={handleMenuClick}\n                style={{ borderRight: 0 }}\n              />\n            </div>\n          </div>\n        </Sider>\n\n        <Content className=\"layout-main\">\n          {children}\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,MAAM;AAC3F,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,aAAa,QACR,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3B,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGzC,SAAS;AAC5C,MAAM;EAAE0C;AAAM,CAAC,GAAGpC,QAAQ;AAE1B,MAAMP,MAAM,GAAGA,CAAC;EAAE4C;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMqD,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM6C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAGF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,IAAIA,SAAS,CAAC,CAAC,CAAC,EAAE;MAC/C,OAAOA,SAAS,CAAC,CAAC,CAAC;IACrB;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,SAAS,GAAGJ,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAMK,aAAa,GAAG,CACpB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAExB,OAAA,CAAC3B,iBAAiB;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAExB,OAAA,CAAC1B,eAAe;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,gBAAgB,GAAGT,SAAS,GAAG,CACnC;IACEE,GAAG,EAAE,aAAaF,SAAS,aAAa;IACxCG,IAAI,eAAExB,OAAA,CAACzB,YAAY;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,WAAW;IACtCG,IAAI,eAAExB,OAAA,CAACxB,YAAY;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,QAAQ;IACnCG,IAAI,eAAExB,OAAA,CAACvB,YAAY;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,UAAU;IACrCG,IAAI,eAAExB,OAAA,CAACtB,gBAAgB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,wBAAwB;IACnDG,IAAI,eAAExB,OAAA,CAACX,mBAAmB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,oBAAoB;IAC/CG,IAAI,eAAExB,OAAA,CAACxB,YAAY;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,gBAAgB;IAC3CG,IAAI,eAAExB,OAAA,CAACV,WAAW;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,CACF,GAAG,EAAE;;EAEN;EACA,MAAME,iBAAiB,GAAGV,SAAS,GAAG,CACpC;IACEE,GAAG,EAAE,aAAaF,SAAS,iBAAiB;IAC5CG,IAAI,eAAExB,OAAA,CAACrB,cAAc;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,sBAAsB;IACjDG,IAAI,eAAExB,OAAA,CAACpB,mBAAmB;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,oBAAoB;IAC/CG,IAAI,eAAExB,OAAA,CAACT,gBAAgB;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,cAAc;IACzCG,IAAI,eAAExB,OAAA,CAACR,aAAa;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,gBAAgB;IAC3CG,IAAI,eAAExB,OAAA,CAACP,eAAe;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,sBAAsB;IACjDG,IAAI,eAAExB,OAAA,CAACN,YAAY;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,6BAA6B;IACxDG,IAAI,eAAExB,OAAA,CAACL,aAAa;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,mBAAmB;IAC9CG,IAAI,eAAExB,OAAA,CAACxB,YAAY;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,mBAAmB;IAC9CG,IAAI,eAAExB,OAAA,CAACJ,YAAY;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,qBAAqB;IAChDG,IAAI,eAAExB,OAAA,CAACzB,YAAY;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,WAAW;IACtCG,IAAI,eAAExB,OAAA,CAACnB,mBAAmB;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,YAAY;IACvCG,IAAI,eAAExB,OAAA,CAAClB,gBAAgB;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF,GAAG,EAAE;;EAEN;EACA,MAAMG,cAAc,GAAG,CACrB;IACET,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAExB,OAAA,CAACjB,aAAa;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAExB,OAAA,CAACjB,aAAa;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAExB,OAAA,CAAChB,eAAe;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMI,aAAa,GAAG,CACpB;IACEV,GAAG,EAAE,SAAS;IACdC,IAAI,eAAExB,OAAA,CAACzB,YAAY;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAExB,OAAA,CAAChB,eAAe;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEK,IAAI,EAAE;EACR,CAAC,EACD;IACEX,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAExB,OAAA,CAACb,cAAc;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMM,eAAe,GAAGA,CAAC;IAAEZ;EAAI,CAAC,KAAK;IACnCR,QAAQ,CAACQ,GAAG,CAAC;EACf,CAAC;EAED,MAAMa,uBAAuB,GAAIb,GAAG,IAAK;IACvCR,QAAQ,CAACQ,GAAG,CAAC;EACf,CAAC;EAED,MAAMc,mBAAmB,GAAGA,CAAC;IAAEd;EAAI,CAAC,KAAK;IACvC,QAAQA,GAAG;MACT,KAAK,SAAS;QACZR,QAAQ,CAAC,UAAU,CAAC;QACpB;MACF,KAAK,UAAU;QACbA,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF,KAAK,QAAQ;QACX;QACAuB,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;QACnB;MACF;QACE;IACJ;EACF,CAAC;EAED,oBACEvC,OAAA,CAACpC,SAAS;IAAC4E,SAAS,EAAC,kBAAkB;IAAAjC,QAAA,gBACrCP,OAAA,CAACG,MAAM;MAACqC,SAAS,EAAC,eAAe;MAAAjC,QAAA,eAC/BP,OAAA;QAAKyC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAArC,QAAA,gBACrFP,OAAA;UAAKyC,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAApC,QAAA,gBACpDP,OAAA,CAAChC,MAAM;YACLkE,IAAI,EAAC,MAAM;YACXV,IAAI,EAAEf,SAAS,gBAAGT,OAAA,CAACd,kBAAkB;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5B,OAAA,CAACf,gBAAgB;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEiB,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC,CAACD,SAAS,CAAE;YACxCgC,KAAK,EAAE;cAAEK,WAAW,EAAE;YAAG;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF5B,OAAA;YAAIyC,KAAK,EAAE;cAAEM,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAA3C,QAAA,EAAC;UAElF;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN5B,OAAA,CAAC/B,KAAK;UAAAsC,QAAA,gBACJP,OAAA,CAAChC,MAAM;YAACkE,IAAI,EAAC,MAAM;YAACV,IAAI,eAAExB,OAAA,CAACZ,YAAY;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9C5B,OAAA,CAACjC,QAAQ;YACPoF,IAAI,EAAE;cACJC,KAAK,EAAEnB,aAAa;cACpBY,OAAO,EAAER;YACX,CAAE;YACFgB,SAAS,EAAC,aAAa;YAAA9C,QAAA,eAEvBP,OAAA,CAAC/B,KAAK;cAACwE,KAAK,EAAE;gBAAEa,MAAM,EAAE;cAAU,CAAE;cAAA/C,QAAA,gBAClCP,OAAA,CAAClC,MAAM;gBAAC0D,IAAI,eAAExB,OAAA,CAACzB,YAAY;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClC5B,OAAA;gBAAAO,QAAA,EAAM;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET5B,OAAA,CAACpC,SAAS;MAAC4E,SAAS,EAAC,gBAAgB;MAAAjC,QAAA,gBACnCP,OAAA,CAACI,KAAK;QACJoC,SAAS,EAAC,cAAc;QACxB/B,SAAS,EAAEA,SAAU;QACrB8C,KAAK,EAAE,GAAI;QACXC,cAAc,EAAE,EAAG;QACnBC,KAAK,EAAC,OAAO;QAAAlD,QAAA,eAEbP,OAAA;UAAKyC,KAAK,EAAE;YAAEiB,MAAM,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAApD,QAAA,gBAEhDP,OAAA,CAACnC,IAAI;YACH+F,IAAI,EAAC,QAAQ;YACbC,YAAY,EAAE,CAAC7C,QAAQ,CAACG,QAAQ,CAAE;YAClCiC,KAAK,EAAE9B,aAAc;YACrBuB,OAAO,EAAEV,eAAgB;YACzBM,KAAK,EAAE;cAAEqB,WAAW,EAAE,CAAC;cAAEC,YAAY,EAAE;YAAE;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,EAGDP,SAAS,iBACRrB,OAAA,CAAAE,SAAA;YAAAK,QAAA,gBAEEP,OAAA,CAAC9B,QAAQ;cACP8F,KAAK;cACLC,IAAI,EAAC,OAAO;cACZC,SAAS,EAAEvD,gBAAgB,GAAG,EAAE,GAAG,CAAC,SAAS,CAAE;cAC/CwD,QAAQ,EAAGC,IAAI,IAAKxD,mBAAmB,CAAC,CAACwD,IAAI,CAACC,QAAQ,CAAC,SAAS,CAAC,CAAE;cACnE5B,KAAK,EAAE;gBAAEqB,WAAW,EAAE;cAAE,CAAE;cAAAvD,QAAA,eAE1BP,OAAA,CAACM,KAAK;gBACJgE,MAAM,eACJtE,OAAA;kBAAKyC,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAE4B,OAAO,EAAE;kBAAQ,CAAE;kBAAAhE,QAAA,gBACtEP,OAAA,CAACvB,YAAY;oBAACgE,KAAK,EAAE;sBAAEK,WAAW,EAAE;oBAAE;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC3C5B,OAAA;oBAAMyC,KAAK,EAAE;sBAAES,UAAU,EAAE;oBAAI,CAAE;oBAAA3C,QAAA,EAAC;kBAAI;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CACN;gBAED4C,SAAS,EAAE,CAAC/D,SAAU;gBAAAF,QAAA,eAEtBP,OAAA,CAACnC,IAAI;kBACH+F,IAAI,EAAC,QAAQ;kBACbC,YAAY,EAAE,CAAC7C,QAAQ,CAACG,QAAQ,CAAE;kBAClCiC,KAAK,EAAEtB,gBAAiB;kBACxBe,OAAO,EAAEV,eAAgB;kBACzBM,KAAK,EAAE;oBAAEqB,WAAW,EAAE,CAAC;oBAAEW,eAAe,EAAE;kBAAc,CAAE;kBAC1DC,YAAY,EAAEjE,SAAS,GAAG,EAAE,GAAG;gBAAG;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC,GAVE,SAAS;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGX5B,OAAA,CAAC9B,QAAQ;cACP8F,KAAK;cACLC,IAAI,EAAC,OAAO;cACZC,SAAS,EAAErD,iBAAiB,GAAG,EAAE,GAAG,CAAC,UAAU,CAAE;cACjDsD,QAAQ,EAAGC,IAAI,IAAKtD,oBAAoB,CAAC,CAACsD,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAE;cACrE5B,KAAK,EAAE;gBAAEqB,WAAW,EAAE;cAAE,CAAE;cAAAvD,QAAA,eAE1BP,OAAA,CAACM,KAAK;gBACJgE,MAAM,eACJtE,OAAA;kBAAKyC,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAE4B,OAAO,EAAE;kBAAQ,CAAE;kBAAAhE,QAAA,gBACtEP,OAAA,CAAChB,eAAe;oBAACyD,KAAK,EAAE;sBAAEK,WAAW,EAAE;oBAAE;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C5B,OAAA;oBAAMyC,KAAK,EAAE;sBAAES,UAAU,EAAE;oBAAI,CAAE;oBAAA3C,QAAA,EAAC;kBAAI;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CACN;gBAED4C,SAAS,EAAE,CAAC/D,SAAU;gBAAAF,QAAA,eAEtBP,OAAA,CAACnC,IAAI;kBACH+F,IAAI,EAAC,QAAQ;kBACbC,YAAY,EAAE,CAAC7C,QAAQ,CAACG,QAAQ,CAAE;kBAClCiC,KAAK,EAAErB,iBAAkB;kBACzBc,OAAO,EAAEV,eAAgB;kBACzBM,KAAK,EAAE;oBAAEqB,WAAW,EAAE,CAAC;oBAAEW,eAAe,EAAE;kBAAc,CAAE;kBAC1DC,YAAY,EAAEjE,SAAS,GAAG,EAAE,GAAG;gBAAG;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC,GAVE,UAAU;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,eACX,CACH,eAGD5B,OAAA;YAAKwC,SAAS,EAAC,eAAe;YAAAjC,QAAA,gBAC5BP,OAAA;cAAKwC,SAAS,EAAC,aAAa;cAAAjC,QAAA,EAAC;YAE7B;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN5B,OAAA,CAACnC,IAAI;cACH+F,IAAI,EAAC,QAAQ;cACbC,YAAY,EAAE,CAAC7C,QAAQ,CAACG,QAAQ,CAAE;cAClCiC,KAAK,EAAEpB,cAAe;cACtBa,OAAO,EAAEV,eAAgB;cACzBM,KAAK,EAAE;gBAAEqB,WAAW,EAAE;cAAE;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER5B,OAAA,CAACK,OAAO;QAACmC,SAAS,EAAC,aAAa;QAAAjC,QAAA,EAC7BA;MAAQ;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACpB,EAAA,CAlVI7C,MAAM;EAAA,QAIOQ,WAAW,EACXC,WAAW;AAAA;AAAAuG,EAAA,GALxBhH,MAAM;AAoVZ,eAAeA,MAAM;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}