# 预置项目功能说明

## 概述

预置项目功能允许系统提供一些预先配置好的项目模板，用户可以查看和复制这些模板，但不能直接编辑或删除它们。这些预置项目通常包含完整的世界设定、人物关系、修炼体系等内容，可以作为创作参考。

## 功能特性

### 1. 预置项目标识
- 预置项目在项目列表中会显示蓝色的"预置"标签
- 预置项目通常以"【预置】"开头命名
- 作者显示为"系统"

### 2. 权限控制
- **查看**：用户可以正常查看预置项目的所有内容
- **编辑**：预置项目不允许编辑，编辑按钮会被禁用
- **删除**：预置项目不允许删除，删除按钮不会显示
- **复制**：用户可以复制预置项目创建自己的版本

### 3. 复制功能
- 用户可以复制预置项目创建自己的项目
- 复制的项目会自动添加"(副本)"后缀
- 复制的项目不再是预置项目，用户可以自由编辑

## 技术实现

### 数据库结构
```sql
ALTER TABLE projects 
ADD COLUMN is_preset BOOLEAN DEFAULT FALSE COMMENT '是否为预置项目';
```

### 后端API
- 在项目更新和删除接口中添加预置项目检查
- 如果尝试编辑或删除预置项目，返回错误信息

### 前端界面
- 在项目列表中显示预置项目标识
- 禁用预置项目的编辑和删除按钮
- 在操作时进行二次检查

## 预置项目类型

### 1. 玄幻世界模板
- **名称**：【预置】玄幻世界模板
- **类型**：奇幻
- **内容**：完整的修仙世界设定，包含修炼体系、门派势力、法宝装备等

### 2. 现代都市模板
- **名称**：【预置】现代都市模板
- **类型**：现代
- **内容**：都市异能世界设定，包含异能体系、组织势力、现代科技等

### 3. 科幻星际模板
- **名称**：【预置】科幻星际模板
- **类型**：科幻
- **内容**：星际文明设定，包含科技体系、文明等级、宇宙结构等

## 使用方法

### 1. 查看预置项目
1. 进入项目管理页面
2. 在项目列表中找到带有"预置"标签的项目
3. 点击"查看"按钮查看项目详情

### 2. 复制预置项目
1. 在项目列表中找到要复制的预置项目
2. 点击"复制"按钮
3. 输入新项目名称
4. 确认复制，系统会创建一个可编辑的副本

### 3. 基于预置项目创作
1. 复制感兴趣的预置项目
2. 在副本中修改和完善内容
3. 添加自己的创意和设定
4. 开始创作

## 管理员功能

### 1. 创建预置项目
```python
# 运行初始化脚本
python backend/scripts/init_preset_projects.py
```

### 2. 数据库迁移
```python
# 添加is_preset字段
python backend/migrations/add_is_preset_to_projects.py
```

### 3. 标记现有项目为预置
```sql
UPDATE projects 
SET is_preset = TRUE 
WHERE name LIKE '%预置%' OR author = '系统';
```

## 注意事项

1. **数据完整性**：预置项目应该包含完整的设定内容
2. **命名规范**：预置项目建议以"【预置】"开头
3. **权限控制**：确保预置项目不能被意外修改或删除
4. **版本管理**：预置项目的更新需要谨慎处理
5. **备份策略**：定期备份预置项目数据

## 故障排除

### 1. 预置项目可以被编辑
- 检查数据库中is_preset字段是否正确设置
- 确认前端权限检查逻辑是否正常

### 2. 预置项目标识不显示
- 检查前端渲染逻辑
- 确认API返回的数据包含is_preset字段

### 3. 复制功能异常
- 检查复制逻辑是否正确设置is_preset为false
- 确认新项目名称生成逻辑

## 扩展功能

### 1. 预置项目分类
- 可以添加category字段对预置项目进行分类
- 支持按类型筛选预置项目

### 2. 预置项目版本控制
- 可以添加版本管理功能
- 支持预置项目的更新和回滚

### 3. 预置项目评分
- 用户可以对预置项目进行评分
- 根据评分推荐热门模板
