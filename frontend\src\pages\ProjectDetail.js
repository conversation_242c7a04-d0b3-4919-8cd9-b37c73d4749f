import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Descriptions,
  message
} from 'antd';
import {
  EditOutlined,
  SettingOutlined,
  ExportOutlined,
  BackwardOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const ProjectDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);

  // 模拟项目数据
  const mockProject = {
    id: 1,
    name: '仙侠传说',
    title: '九天仙缘录',
    author: '作者A',
    type: 'xianxia',
    status: 'writing',
    summary: '这是一个关于修仙者在九天之上寻找仙缘的故事...',
    description: '详细的项目描述...',
    wordCount: 89000,
    chapterCount: 45,
    characterCount: 12,
    factionCount: 8,
    plotCount: 15,
    progress: 65,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  };

  useEffect(() => {
    loadProject();
  }, [id]);

  const loadProject = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setProject(mockProject);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('加载项目详情失败');
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      planning: 'blue',
      writing: 'green',
      reviewing: 'orange',
      completed: 'purple',
      published: 'gold'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      planning: '规划中',
      writing: '写作中',
      reviewing: '审阅中',
      completed: '已完成',
      published: '已发布'
    };
    return texts[status] || status;
  };

  const getTypeText = (type) => {
    const texts = {
      fantasy: '奇幻',
      xianxia: '仙侠',
      wuxia: '武侠',
      scifi: '科幻',
      modern: '现代',
      historical: '历史',
      romance: '言情'
    };
    return texts[type] || type;
  };

  if (loading || !project) {
    return <div className="loading-container">加载中...</div>;
  }

  return (
    <div className="fade-in">
      <div className="page-header">
        <Space>
          <Button
            icon={<BackwardOutlined />}
            onClick={() => navigate('/projects')}
          >
            返回项目列表
          </Button>
          <Title level={2} className="page-title">{project.name}</Title>
          <Tag color={getStatusColor(project.status)}>
            {getStatusText(project.status)}
          </Tag>
          <Tag>{getTypeText(project.type)}</Tag>
        </Space>

        <Space style={{ marginTop: 16 }}>
          <Button type="primary" icon={<EditOutlined />}>
            编辑项目
          </Button>
          <Button icon={<SettingOutlined />}>
            项目设置
          </Button>
          <Button icon={<ExportOutlined />}>
            导出项目
          </Button>
        </Space>
      </div>

      {/* 项目统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总字数"
              value={project.wordCount}
              formatter={(value) => `${(value / 10000).toFixed(1)}万`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="章节数"
              value={project.chapterCount}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="人物数"
              value={project.characterCount}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="完成进度"
              value={project.progress}
              suffix="%"
              valueStyle={{ color: '#fa8c16' }}
            />
            <Progress
              percent={project.progress}
              size="small"
              status={project.progress === 100 ? 'success' : 'active'}
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 项目管理 */}
      <Card>
        <Tabs defaultActiveKey="overview">
          <TabPane tab="项目概览" key="overview">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="项目名称">{project.name}</Descriptions.Item>
              <Descriptions.Item label="小说标题">{project.title}</Descriptions.Item>
              <Descriptions.Item label="作者">{project.author}</Descriptions.Item>
              <Descriptions.Item label="项目类型">{getTypeText(project.type)}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{project.createdAt}</Descriptions.Item>
              <Descriptions.Item label="最后修改">{project.updatedAt}</Descriptions.Item>
              <Descriptions.Item label="项目简介" span={2}>
                {project.summary}
              </Descriptions.Item>
              <Descriptions.Item label="详细描述" span={2}>
                {project.description}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane tab="卷宗管理" key="volumes">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="卷宗管理"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/volumes`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={project.volumeCount || 0} suffix="个卷宗" />
                  <Text type="secondary">管理小说卷宗结构</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="章节管理"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/volumes`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={project.chapterCount} suffix="个章节" />
                  <Text type="secondary">管理章节内容</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="写作进度"
                  extra={<Button type="link">查看详情</Button>}
                  onClick={() => navigate(`/projects/${id}/volumes`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={project.progress} suffix="%" />
                  <Text type="secondary">整体创作进度</Text>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="内容管理" key="content">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="人物管理"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/characters`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={project.characterCount} suffix="个人物" />
                  <Text type="secondary">管理角色档案</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="势力管理"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/factions`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={project.factionCount} suffix="个势力" />
                  <Text type="secondary">管理势力组织</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="剧情管理"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/plots`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={project.plotCount} suffix="个剧情" />
                  <Text type="secondary">管理剧情线索</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="资源分布"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/resource-distribution`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={0} suffix="个资源点" />
                  <Text type="secondary">管理资源分布</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="种族分布"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/race-distribution`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={0} suffix="个种族区域" />
                  <Text type="secondary">管理种族分布</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="秘境分布"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/secret-realm-distribution`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Statistic value={0} suffix="个秘境" />
                  <Text type="secondary">管理秘境分布</Text>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="设定管理" key="settings">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="世界设定"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/world-settings`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>世界观设定</Text>
                  <br />
                  <Text type="secondary">地理、历史、文化</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="修炼体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/cultivation-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>修炼体系</Text>
                  <br />
                  <Text type="secondary">能力、等级、方法</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="政治体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/political-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>政治体系</Text>
                  <br />
                  <Text type="secondary">政府、法律、权力</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="货币体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/currency-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>货币体系</Text>
                  <br />
                  <Text type="secondary">货币、金融、经济</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="商业体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/commerce-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>商业体系</Text>
                  <br />
                  <Text type="secondary">贸易、商会、市场</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="种族类别"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/race-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>种族类别</Text>
                  <br />
                  <Text type="secondary">种族、特性、关系</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="功法体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/martial-arts-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>功法体系</Text>
                  <br />
                  <Text type="secondary">功法、招式、传承</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="装备体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/equipment-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>装备体系</Text>
                  <br />
                  <Text type="secondary">装备、强化、套装</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="宠物体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/pet-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>宠物体系</Text>
                  <br />
                  <Text type="secondary">宠物、进化、培养</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="地图结构"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/map-structures`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>地图结构</Text>
                  <br />
                  <Text type="secondary">地图、地形、区域</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="维度结构"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/dimension-structures`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>维度结构</Text>
                  <br />
                  <Text type="secondary">维度、法则、传送</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="灵宝体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/spiritual-treasure-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>灵宝体系</Text>
                  <br />
                  <Text type="secondary">灵宝、器灵、炼制</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="生民体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/civilian-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>生民体系</Text>
                  <br />
                  <Text type="secondary">人口、社会、生活</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="司法体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/judicial-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>司法体系</Text>
                  <br />
                  <Text type="secondary">法院、执法、审判</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="职业体系"
                  extra={<Button type="link">查看全部</Button>}
                  onClick={() => navigate(`/projects/${id}/profession-systems`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>职业体系</Text>
                  <br />
                  <Text type="secondary">职业、技能、组织</Text>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="工具" key="tools">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="时间线"
                  extra={<Button type="link">查看</Button>}
                  onClick={() => navigate(`/projects/${id}/timeline`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>查看项目时间线</Text>
                  <br />
                  <Text type="secondary">事件、发展、历史</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="关系网络"
                  extra={<Button type="link">查看</Button>}
                  onClick={() => navigate(`/projects/${id}/relations`)}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>人物关系图谱</Text>
                  <br />
                  <Text type="secondary">人物、势力、关系</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="AI助手"
                  extra={<Button type="link">使用</Button>}
                  onClick={() => navigate('/ai-assistant')}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>AI辅助创作</Text>
                  <br />
                  <Text type="secondary">智能生成、续写、分析</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="AI测试"
                  extra={<Button type="link">测试</Button>}
                  onClick={() => navigate('/ai-test')}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>AI功能测试</Text>
                  <br />
                  <Text type="secondary">测试AI模型功能</Text>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card
                  title="系统设置"
                  extra={<Button type="link">设置</Button>}
                  onClick={() => navigate('/settings')}
                  style={{ cursor: 'pointer' }}
                >
                  <Text>系统配置</Text>
                  <br />
                  <Text type="secondary">AI配置、系统设置</Text>
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ProjectDetail;
