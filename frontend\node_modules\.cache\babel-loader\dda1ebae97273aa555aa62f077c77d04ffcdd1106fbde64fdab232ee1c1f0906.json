{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\OllamaTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Button, Space, Alert, List, Tag, Typography, Spin, message, Row, Col, Statistic } from 'antd';\nimport { ReloadOutlined, RobotOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst OllamaTest = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [models, setModels] = useState([]);\n  const [ollamaStatus, setOllamaStatus] = useState(null);\n  const [error, setError] = useState(null);\n\n  // 获取Ollama模型列表\n  const fetchOllamaModels = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await axios.get('/api/ai/ollama/models');\n      console.log('Ollama models response:', response.data);\n      setModels(response.data.models);\n      message.success(`成功获取 ${response.data.count} 个模型`);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('获取Ollama模型失败:', error);\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message);\n      message.error('获取Ollama模型失败');\n      setModels([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 检查Ollama状态\n  const checkOllamaStatus = async () => {\n    try {\n      // 先切换到ollama提供商\n      await axios.post('/api/ai/switch-provider', {\n        provider: 'ollama'\n      });\n\n      // 然后检查状态\n      const response = await axios.get('/api/ai/status');\n      setOllamaStatus(response.data);\n    } catch (error) {\n      console.error('检查Ollama状态失败:', error);\n      setOllamaStatus({\n        connected: false,\n        status: 'error',\n        error: error.message\n      });\n    }\n  };\n\n  // 获取模型详细信息\n  const getModelInfo = async modelName => {\n    try {\n      const response = await axios.get(`/api/v1/ai/ollama/models/${modelName}`);\n      console.log(`Model ${modelName} info:`, response.data);\n      message.success(`获取模型 ${modelName} 信息成功`);\n    } catch (error) {\n      console.error(`获取模型 ${modelName} 信息失败:`, error);\n      message.error(`获取模型 ${modelName} 信息失败`);\n    }\n  };\n\n  // 格式化文件大小\n  const formatSize = bytes => {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n  };\n  useEffect(() => {\n    checkOllamaStatus();\n    fetchOllamaModels();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px',\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), \" Ollama\\u6A21\\u578B\\u68C0\\u6D4B\\u6D4B\\u8BD5\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Ollama\\u72B6\\u6001\",\n            value: ollamaStatus !== null && ollamaStatus !== void 0 && ollamaStatus.connected ? '在线' : '离线',\n            prefix: ollamaStatus !== null && ollamaStatus !== void 0 && ollamaStatus.connected ? /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 49\n            }, this) : /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n              style: {\n                color: '#ff4d4f'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 104\n            }, this),\n            valueStyle: {\n              color: ollamaStatus !== null && ollamaStatus !== void 0 && ollamaStatus.connected ? '#52c41a' : '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u672C\\u5730\\u6A21\\u578B\\u6570\\u91CF\",\n            value: models.length,\n            prefix: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5927\\u5C0F\",\n            value: formatSize(models.reduce((total, model) => total + (model.size || 0), 0)),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 19\n          }, this),\n          onClick: fetchOllamaModels,\n          loading: loading,\n          children: \"\\u5237\\u65B0\\u6A21\\u578B\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 19\n          }, this),\n          onClick: checkOllamaStatus,\n          children: \"\\u68C0\\u67E5\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u9519\\u8BEF\",\n      description: error,\n      type: \"error\",\n      showIcon: true,\n      style: {\n        marginBottom: '24px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 9\n    }, this), ollamaStatus && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"Ollama\\u670D\\u52A1\\u72B6\\u6001\",\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u63D0\\u4F9B\\u5546: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: ollamaStatus.provider\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u8FDE\\u63A5\\u72B6\\u6001: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: ollamaStatus.connected ? 'green' : 'red',\n            children: ollamaStatus.connected ? '已连接' : '未连接'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u670D\\u52A1\\u72B6\\u6001: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: ollamaStatus.status === 'online' ? 'green' : 'orange',\n            children: ollamaStatus.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), ollamaStatus.error && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u9519\\u8BEF\\u4FE1\\u606F: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"danger\",\n            children: ollamaStatus.error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: `本地模型列表 (${models.length})`,\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spin, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '16px'\n          },\n          children: \"\\u6B63\\u5728\\u83B7\\u53D6\\u6A21\\u578B\\u5217\\u8868...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this) : models.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n        dataSource: models,\n        renderItem: model => /*#__PURE__*/_jsxDEV(List.Item, {\n          actions: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            onClick: () => getModelInfo(model.name),\n            children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 19\n          }, this)],\n          children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: model.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: formatSize(model.size)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 21\n            }, this),\n            description: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u4FEE\\u6539\\u65F6\\u95F4: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: new Date(model.modified_at).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 23\n              }, this), model.digest && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u6458\\u8981: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  code: true,\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: [model.digest.substring(0, 16), \"...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n          style: {\n            fontSize: '48px',\n            color: '#faad14',\n            marginBottom: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u672A\\u68C0\\u6D4B\\u5230\\u672C\\u5730Ollama\\u6A21\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u8BF7\\u786E\\u4FDDOllama\\u670D\\u52A1\\u6B63\\u5728\\u8FD0\\u884C\\uFF0C\\u5E76\\u4F7F\\u7528\\u4EE5\\u4E0B\\u547D\\u4EE4\\u4E0B\\u8F7D\\u6A21\\u578B\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            code: true,\n            children: \"ollama pull llama2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            code: true,\n            children: \"ollama pull mistral\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            code: true,\n            children: \"ollama pull qwen\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(OllamaTest, \"skIzcmi9RkonHEaSvHLQE/jwE9g=\");\n_c = OllamaTest;\nexport default OllamaTest;\nvar _c;\n$RefreshReg$(_c, \"OllamaTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON>", "Space", "<PERSON><PERSON>", "List", "Tag", "Typography", "Spin", "message", "Row", "Col", "Statistic", "ReloadOutlined", "RobotOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "axios", "jsxDEV", "_jsxDEV", "Title", "Text", "OllamaTest", "_s", "loading", "setLoading", "models", "setModels", "ollama<PERSON>tatus", "setOllamaStatus", "error", "setError", "fetchOllamaModels", "response", "get", "console", "log", "data", "success", "count", "_error$response", "_error$response$data", "detail", "checkOllamaStatus", "post", "provider", "connected", "status", "getModelInfo", "modelName", "formatSize", "bytes", "k", "sizes", "i", "Math", "floor", "parseFloat", "pow", "toFixed", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "marginBottom", "span", "title", "value", "prefix", "color", "valueStyle", "length", "reduce", "total", "model", "size", "type", "icon", "onClick", "description", "showIcon", "direction", "width", "strong", "textAlign", "marginTop", "dataSource", "renderItem", "<PERSON><PERSON>", "actions", "name", "Meta", "Date", "modified_at", "toLocaleString", "digest", "code", "fontSize", "substring", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/OllamaTest.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Button,\n  Space,\n  Alert,\n  List,\n  Tag,\n  Typography,\n  Spin,\n  message,\n  Row,\n  Col,\n  Statistic\n} from 'antd';\nimport {\n  ReloadOutlined,\n  RobotOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport axios from 'axios';\n\nconst { Title, Text } = Typography;\n\nconst OllamaTest = () => {\n  const [loading, setLoading] = useState(false);\n  const [models, setModels] = useState([]);\n  const [ollamaStatus, setOllamaStatus] = useState(null);\n  const [error, setError] = useState(null);\n\n  // 获取Ollama模型列表\n  const fetchOllamaModels = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await axios.get('/api/ai/ollama/models');\n      console.log('Ollama models response:', response.data);\n\n      setModels(response.data.models);\n      message.success(`成功获取 ${response.data.count} 个模型`);\n    } catch (error) {\n      console.error('获取Ollama模型失败:', error);\n      setError(error.response?.data?.detail || error.message);\n      message.error('获取Ollama模型失败');\n      setModels([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 检查Ollama状态\n  const checkOllamaStatus = async () => {\n    try {\n      // 先切换到ollama提供商\n      await axios.post('/api/ai/switch-provider', { provider: 'ollama' });\n\n      // 然后检查状态\n      const response = await axios.get('/api/ai/status');\n      setOllamaStatus(response.data);\n    } catch (error) {\n      console.error('检查Ollama状态失败:', error);\n      setOllamaStatus({ connected: false, status: 'error', error: error.message });\n    }\n  };\n\n  // 获取模型详细信息\n  const getModelInfo = async (modelName) => {\n    try {\n      const response = await axios.get(`/api/v1/ai/ollama/models/${modelName}`);\n      console.log(`Model ${modelName} info:`, response.data);\n      message.success(`获取模型 ${modelName} 信息成功`);\n    } catch (error) {\n      console.error(`获取模型 ${modelName} 信息失败:`, error);\n      message.error(`获取模型 ${modelName} 信息失败`);\n    }\n  };\n\n  // 格式化文件大小\n  const formatSize = (bytes) => {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n  };\n\n  useEffect(() => {\n    checkOllamaStatus();\n    fetchOllamaModels();\n  }, []);\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n      <Title level={2}>\n        <RobotOutlined /> Ollama模型检测测试\n      </Title>\n\n      {/* 状态概览 */}\n      <Row gutter={24} style={{ marginBottom: '24px' }}>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"Ollama状态\"\n              value={ollamaStatus?.connected ? '在线' : '离线'}\n              prefix={ollamaStatus?.connected ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}\n              valueStyle={{ color: ollamaStatus?.connected ? '#52c41a' : '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"本地模型数量\"\n              value={models.length}\n              prefix={<RobotOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"总大小\"\n              value={formatSize(models.reduce((total, model) => total + (model.size || 0), 0))}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作按钮 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<ReloadOutlined />}\n            onClick={fetchOllamaModels}\n            loading={loading}\n          >\n            刷新模型列表\n          </Button>\n          <Button\n            icon={<CheckCircleOutlined />}\n            onClick={checkOllamaStatus}\n          >\n            检查状态\n          </Button>\n        </Space>\n      </Card>\n\n      {/* 错误信息 */}\n      {error && (\n        <Alert\n          message=\"错误\"\n          description={error}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n      )}\n\n      {/* Ollama状态信息 */}\n      {ollamaStatus && (\n        <Card title=\"Ollama服务状态\" style={{ marginBottom: '24px' }}>\n          <Space direction=\"vertical\" style={{ width: '100%' }}>\n            <div>\n              <Text strong>提供商: </Text>\n              <Tag color=\"blue\">{ollamaStatus.provider}</Tag>\n            </div>\n            <div>\n              <Text strong>连接状态: </Text>\n              <Tag color={ollamaStatus.connected ? 'green' : 'red'}>\n                {ollamaStatus.connected ? '已连接' : '未连接'}\n              </Tag>\n            </div>\n            <div>\n              <Text strong>服务状态: </Text>\n              <Tag color={ollamaStatus.status === 'online' ? 'green' : 'orange'}>\n                {ollamaStatus.status}\n              </Tag>\n            </div>\n            {ollamaStatus.error && (\n              <div>\n                <Text strong>错误信息: </Text>\n                <Text type=\"danger\">{ollamaStatus.error}</Text>\n              </div>\n            )}\n          </Space>\n        </Card>\n      )}\n\n      {/* 模型列表 */}\n      <Card title={`本地模型列表 (${models.length})`}>\n        {loading ? (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <Spin size=\"large\" />\n            <div style={{ marginTop: '16px' }}>正在获取模型列表...</div>\n          </div>\n        ) : models.length > 0 ? (\n          <List\n            dataSource={models}\n            renderItem={(model) => (\n              <List.Item\n                actions={[\n                  <Button\n                    type=\"link\"\n                    onClick={() => getModelInfo(model.name)}\n                  >\n                    查看详情\n                  </Button>\n                ]}\n              >\n                <List.Item.Meta\n                  title={\n                    <Space>\n                      <Text strong>{model.name}</Text>\n                      <Tag color=\"blue\">{formatSize(model.size)}</Tag>\n                    </Space>\n                  }\n                  description={\n                    <Space direction=\"vertical\" size=\"small\">\n                      <div>\n                        <Text type=\"secondary\">修改时间: </Text>\n                        <Text>{new Date(model.modified_at).toLocaleString()}</Text>\n                      </div>\n                      {model.digest && (\n                        <div>\n                          <Text type=\"secondary\">摘要: </Text>\n                          <Text code style={{ fontSize: '12px' }}>\n                            {model.digest.substring(0, 16)}...\n                          </Text>\n                        </div>\n                      )}\n                    </Space>\n                  }\n                />\n              </List.Item>\n            )}\n          />\n        ) : (\n          <div style={{ textAlign: 'center', padding: '40px' }}>\n            <ExclamationCircleOutlined style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }} />\n            <div style={{ marginBottom: '16px' }}>\n              <Text>未检测到本地Ollama模型</Text>\n            </div>\n            <div>\n              <Text type=\"secondary\">\n                请确保Ollama服务正在运行，并使用以下命令下载模型：\n              </Text>\n            </div>\n            <div style={{ marginTop: '8px' }}>\n              <Text code>ollama pull llama2</Text>\n              <br />\n              <Text code>ollama pull mistral</Text>\n              <br />\n              <Text code>ollama pull qwen</Text>\n            </div>\n          </div>\n        )}\n      </Card>\n    </div>\n  );\n};\n\nexport default OllamaTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SACEC,cAAc,EACdC,aAAa,EACbC,mBAAmB,EACnBC,yBAAyB,QACpB,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGd,UAAU;AAElC,MAAMe,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMiC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBM,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAME,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,uBAAuB,CAAC;MACzDC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,QAAQ,CAACI,IAAI,CAAC;MAErDV,SAAS,CAACM,QAAQ,CAACI,IAAI,CAACX,MAAM,CAAC;MAC/BjB,OAAO,CAAC6B,OAAO,CAAC,QAAQL,QAAQ,CAACI,IAAI,CAACE,KAAK,MAAM,CAAC;IACpD,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAU,eAAA,EAAAC,oBAAA;MACdN,OAAO,CAACL,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCC,QAAQ,CAAC,EAAAS,eAAA,GAAAV,KAAK,CAACG,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAIZ,KAAK,CAACrB,OAAO,CAAC;MACvDA,OAAO,CAACqB,KAAK,CAAC,cAAc,CAAC;MAC7BH,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF;MACA,MAAM1B,KAAK,CAAC2B,IAAI,CAAC,yBAAyB,EAAE;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;;MAEnE;MACA,MAAMZ,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,gBAAgB,CAAC;MAClDL,eAAe,CAACI,QAAQ,CAACI,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCD,eAAe,CAAC;QAAEiB,SAAS,EAAE,KAAK;QAAEC,MAAM,EAAE,OAAO;QAAEjB,KAAK,EAAEA,KAAK,CAACrB;MAAQ,CAAC,CAAC;IAC9E;EACF,CAAC;;EAED;EACA,MAAMuC,YAAY,GAAG,MAAOC,SAAS,IAAK;IACxC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,4BAA4Be,SAAS,EAAE,CAAC;MACzEd,OAAO,CAACC,GAAG,CAAC,SAASa,SAAS,QAAQ,EAAEhB,QAAQ,CAACI,IAAI,CAAC;MACtD5B,OAAO,CAAC6B,OAAO,CAAC,QAAQW,SAAS,OAAO,CAAC;IAC3C,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,QAAQmB,SAAS,QAAQ,EAAEnB,KAAK,CAAC;MAC/CrB,OAAO,CAACqB,KAAK,CAAC,QAAQmB,SAAS,OAAO,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAC7B,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3C,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACnB,GAAG,CAACe,KAAK,CAAC,GAAGI,IAAI,CAACnB,GAAG,CAACgB,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAGI,IAAI,CAACG,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAEDtD,SAAS,CAAC,MAAM;IACd2C,iBAAiB,CAAC,CAAC;IACnBX,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEb,OAAA;IAAKyC,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACpE7C,OAAA,CAACC,KAAK;MAAC6C,KAAK,EAAE,CAAE;MAAAD,QAAA,gBACd7C,OAAA,CAACL,aAAa;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,+CACnB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAGRlD,OAAA,CAACT,GAAG;MAAC4D,MAAM,EAAE,EAAG;MAACV,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,gBAC/C7C,OAAA,CAACR,GAAG;QAAC6D,IAAI,EAAE,CAAE;QAAAR,QAAA,eACX7C,OAAA,CAAClB,IAAI;UAAA+D,QAAA,eACH7C,OAAA,CAACP,SAAS;YACR6D,KAAK,EAAC,oBAAU;YAChBC,KAAK,EAAE9C,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEkB,SAAS,GAAG,IAAI,GAAG,IAAK;YAC7C6B,MAAM,EAAE/C,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEkB,SAAS,gBAAG3B,OAAA,CAACJ,mBAAmB;cAAC6C,KAAK,EAAE;gBAAEgB,KAAK,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACH,yBAAyB;cAAC4C,KAAK,EAAE;gBAAEgB,KAAK,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACpJQ,UAAU,EAAE;cAAED,KAAK,EAAEhD,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEkB,SAAS,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACR,GAAG;QAAC6D,IAAI,EAAE,CAAE;QAAAR,QAAA,eACX7C,OAAA,CAAClB,IAAI;UAAA+D,QAAA,eACH7C,OAAA,CAACP,SAAS;YACR6D,KAAK,EAAC,sCAAQ;YACdC,KAAK,EAAEhD,MAAM,CAACoD,MAAO;YACrBH,MAAM,eAAExD,OAAA,CAACL,aAAa;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BQ,UAAU,EAAE;cAAED,KAAK,EAAE;YAAU;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACR,GAAG;QAAC6D,IAAI,EAAE,CAAE;QAAAR,QAAA,eACX7C,OAAA,CAAClB,IAAI;UAAA+D,QAAA,eACH7C,OAAA,CAACP,SAAS;YACR6D,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAExB,UAAU,CAACxB,MAAM,CAACqD,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKD,KAAK,IAAIC,KAAK,CAACC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;YACjFL,UAAU,EAAE;cAAED,KAAK,EAAE;YAAU;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA,CAAClB,IAAI;MAAC2D,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,eACpC7C,OAAA,CAAChB,KAAK;QAAA6D,QAAA,gBACJ7C,OAAA,CAACjB,MAAM;UACLiF,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEjE,OAAA,CAACN,cAAc;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBgB,OAAO,EAAErD,iBAAkB;UAC3BR,OAAO,EAAEA,OAAQ;UAAAwC,QAAA,EAClB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlD,OAAA,CAACjB,MAAM;UACLkF,IAAI,eAAEjE,OAAA,CAACJ,mBAAmB;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9BgB,OAAO,EAAE1C,iBAAkB;UAAAqB,QAAA,EAC5B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGNvC,KAAK,iBACJX,OAAA,CAACf,KAAK;MACJK,OAAO,EAAC,cAAI;MACZ6E,WAAW,EAAExD,KAAM;MACnBqD,IAAI,EAAC,OAAO;MACZI,QAAQ;MACR3B,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CACF,EAGAzC,YAAY,iBACXT,OAAA,CAAClB,IAAI;MAACwE,KAAK,EAAC,gCAAY;MAACb,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,eACvD7C,OAAA,CAAChB,KAAK;QAACqF,SAAS,EAAC,UAAU;QAAC5B,KAAK,EAAE;UAAE6B,KAAK,EAAE;QAAO,CAAE;QAAAzB,QAAA,gBACnD7C,OAAA;UAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;YAACqE,MAAM;YAAA1B,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBlD,OAAA,CAACb,GAAG;YAACsE,KAAK,EAAC,MAAM;YAAAZ,QAAA,EAAEpC,YAAY,CAACiB;UAAQ;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNlD,OAAA;UAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;YAACqE,MAAM;YAAA1B,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BlD,OAAA,CAACb,GAAG;YAACsE,KAAK,EAAEhD,YAAY,CAACkB,SAAS,GAAG,OAAO,GAAG,KAAM;YAAAkB,QAAA,EAClDpC,YAAY,CAACkB,SAAS,GAAG,KAAK,GAAG;UAAK;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA;UAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;YAACqE,MAAM;YAAA1B,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BlD,OAAA,CAACb,GAAG;YAACsE,KAAK,EAAEhD,YAAY,CAACmB,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,QAAS;YAAAiB,QAAA,EAC/DpC,YAAY,CAACmB;UAAM;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLzC,YAAY,CAACE,KAAK,iBACjBX,OAAA;UAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;YAACqE,MAAM;YAAA1B,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BlD,OAAA,CAACE,IAAI;YAAC8D,IAAI,EAAC,QAAQ;YAAAnB,QAAA,EAAEpC,YAAY,CAACE;UAAK;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACP,eAGDlD,OAAA,CAAClB,IAAI;MAACwE,KAAK,EAAE,WAAW/C,MAAM,CAACoD,MAAM,GAAI;MAAAd,QAAA,EACtCxC,OAAO,gBACNL,OAAA;QAAKyC,KAAK,EAAE;UAAE+B,SAAS,EAAE,QAAQ;UAAE9B,OAAO,EAAE;QAAO,CAAE;QAAAG,QAAA,gBACnD7C,OAAA,CAACX,IAAI;UAAC0E,IAAI,EAAC;QAAO;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrBlD,OAAA;UAAKyC,KAAK,EAAE;YAAEgC,SAAS,EAAE;UAAO,CAAE;UAAA5B,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,GACJ3C,MAAM,CAACoD,MAAM,GAAG,CAAC,gBACnB3D,OAAA,CAACd,IAAI;QACHwF,UAAU,EAAEnE,MAAO;QACnBoE,UAAU,EAAGb,KAAK,iBAChB9D,OAAA,CAACd,IAAI,CAAC0F,IAAI;UACRC,OAAO,EAAE,cACP7E,OAAA,CAACjB,MAAM;YACLiF,IAAI,EAAC,MAAM;YACXE,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAACiC,KAAK,CAACgB,IAAI,CAAE;YAAAjC,QAAA,EACzC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,CACT;UAAAL,QAAA,eAEF7C,OAAA,CAACd,IAAI,CAAC0F,IAAI,CAACG,IAAI;YACbzB,KAAK,eACHtD,OAAA,CAAChB,KAAK;cAAA6D,QAAA,gBACJ7C,OAAA,CAACE,IAAI;gBAACqE,MAAM;gBAAA1B,QAAA,EAAEiB,KAAK,CAACgB;cAAI;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChClD,OAAA,CAACb,GAAG;gBAACsE,KAAK,EAAC,MAAM;gBAAAZ,QAAA,EAAEd,UAAU,CAAC+B,KAAK,CAACC,IAAI;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACR;YACDiB,WAAW,eACTnE,OAAA,CAAChB,KAAK;cAACqF,SAAS,EAAC,UAAU;cAACN,IAAI,EAAC,OAAO;cAAAlB,QAAA,gBACtC7C,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;kBAAC8D,IAAI,EAAC,WAAW;kBAAAnB,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpClD,OAAA,CAACE,IAAI;kBAAA2C,QAAA,EAAE,IAAImC,IAAI,CAAClB,KAAK,CAACmB,WAAW,CAAC,CAACC,cAAc,CAAC;gBAAC;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,EACLY,KAAK,CAACqB,MAAM,iBACXnF,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;kBAAC8D,IAAI,EAAC,WAAW;kBAAAnB,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClClD,OAAA,CAACE,IAAI;kBAACkF,IAAI;kBAAC3C,KAAK,EAAE;oBAAE4C,QAAQ,EAAE;kBAAO,CAAE;kBAAAxC,QAAA,GACpCiB,KAAK,CAACqB,MAAM,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACjC;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEFlD,OAAA;QAAKyC,KAAK,EAAE;UAAE+B,SAAS,EAAE,QAAQ;UAAE9B,OAAO,EAAE;QAAO,CAAE;QAAAG,QAAA,gBACnD7C,OAAA,CAACH,yBAAyB;UAAC4C,KAAK,EAAE;YAAE4C,QAAQ,EAAE,MAAM;YAAE5B,KAAK,EAAE,SAAS;YAAEL,YAAY,EAAE;UAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClGlD,OAAA;UAAKyC,KAAK,EAAE;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,eACnC7C,OAAA,CAACE,IAAI;YAAA2C,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACNlD,OAAA;UAAA6C,QAAA,eACE7C,OAAA,CAACE,IAAI;YAAC8D,IAAI,EAAC,WAAW;YAAAnB,QAAA,EAAC;UAEvB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNlD,OAAA;UAAKyC,KAAK,EAAE;YAAEgC,SAAS,EAAE;UAAM,CAAE;UAAA5B,QAAA,gBAC/B7C,OAAA,CAACE,IAAI;YAACkF,IAAI;YAAAvC,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpClD,OAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA,CAACE,IAAI;YAACkF,IAAI;YAAAvC,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrClD,OAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlD,OAAA,CAACE,IAAI;YAACkF,IAAI;YAAAvC,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA/OID,UAAU;AAAAoF,EAAA,GAAVpF,UAAU;AAiPhB,eAAeA,UAAU;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}