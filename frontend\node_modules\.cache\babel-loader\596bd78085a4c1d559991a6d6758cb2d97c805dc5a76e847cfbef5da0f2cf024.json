{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space, Collapse } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { DashboardOutlined, ProjectOutlined, UserOutlined, TeamOutlined, BookOutlined, FileTextOutlined, GlobalOutlined, ThunderboltOutlined, ClockCircleOutlined, ShareAltOutlined, RobotOutlined, SettingOutlined, MenuFoldOutlined, MenuUnfoldOutlined, LogoutOutlined, BellOutlined, EnvironmentOutlined, EyeOutlined, ShoppingOutlined, HeartOutlined, CompassOutlined, StarOutlined, CrownOutlined, BankOutlined, DownOutlined, RightOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = AntLayout;\nconst {\n  Panel\n} = Collapse;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [contentCollapsed, setContentCollapsed] = useState(false);\n  const [settingsCollapsed, setSettingsCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n  const projectId = getProjectId();\n\n  // 菜单项配置\n  const menuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    label: '仪表盘'\n  }, {\n    key: '/projects',\n    icon: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    label: '项目管理'\n  },\n  // 只有在项目页面中才显示内容管理菜单\n  ...(projectId ? [{\n    key: 'content',\n    label: '内容管理',\n    type: 'group'\n  }, {\n    key: `/projects/${projectId}/characters`,\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 15\n    }, this),\n    label: '人物管理'\n  }, {\n    key: `/projects/${projectId}/factions`,\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 15\n    }, this),\n    label: '势力管理'\n  }, {\n    key: `/projects/${projectId}/plots`,\n    icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 15\n    }, this),\n    label: '剧情管理'\n  }, {\n    key: `/projects/${projectId}/volumes`,\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 15\n    }, this),\n    label: '卷宗管理'\n  }, {\n    key: `/projects/${projectId}/resource-distribution`,\n    icon: /*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 15\n    }, this),\n    label: '资源分布'\n  }, {\n    key: `/projects/${projectId}/race-distribution`,\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 15\n    }, this),\n    label: '种族分布'\n  }, {\n    key: `/projects/${projectId}/secret-realms`,\n    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 15\n    }, this),\n    label: '秘境分布'\n  }, {\n    key: 'settings',\n    label: '设定管理',\n    type: 'group'\n  }, {\n    key: `/projects/${projectId}/world-settings`,\n    icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 15\n    }, this),\n    label: '世界设定'\n  }, {\n    key: `/projects/${projectId}/cultivation-systems`,\n    icon: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 15\n    }, this),\n    label: '修炼体系'\n  }, {\n    key: `/projects/${projectId}/equipment-systems`,\n    icon: /*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 15\n    }, this),\n    label: '装备体系'\n  }, {\n    key: `/projects/${projectId}/pet-systems`,\n    icon: /*#__PURE__*/_jsxDEV(HeartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 15\n    }, this),\n    label: '宠物体系'\n  }, {\n    key: `/projects/${projectId}/map-structure`,\n    icon: /*#__PURE__*/_jsxDEV(CompassOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 15\n    }, this),\n    label: '地图结构'\n  }, {\n    key: `/projects/${projectId}/dimension-structure`,\n    icon: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 15\n    }, this),\n    label: '维度结构'\n  }, {\n    key: `/projects/${projectId}/spiritual-treasure-systems`,\n    icon: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 15\n    }, this),\n    label: '灵宝体系'\n  }, {\n    key: `/projects/${projectId}/civilian-systems`,\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 15\n    }, this),\n    label: '生民体系'\n  }, {\n    key: `/projects/${projectId}/judicial-systems`,\n    icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 15\n    }, this),\n    label: '司法体系'\n  }, {\n    key: `/projects/${projectId}/profession-systems`,\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 15\n    }, this),\n    label: '职业体系'\n  }, {\n    key: `/projects/${projectId}/timeline`,\n    icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 15\n    }, this),\n    label: '时间线'\n  }, {\n    key: `/projects/${projectId}/relations`,\n    icon: /*#__PURE__*/_jsxDEV(ShareAltOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 15\n    }, this),\n    label: '关系网络'\n  }] : []), {\n    key: 'tools',\n    label: '工具',\n    type: 'group'\n  }, {\n    key: '/ai-assistant',\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 13\n    }, this),\n    label: 'AI助手'\n  }, {\n    key: '/ai-test',\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 13\n    }, this),\n    label: 'AI测试'\n  }, {\n    key: '/settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 13\n    }, this),\n    label: '系统设置'\n  }];\n\n  // 用户菜单\n  const userMenuItems = [{\n    key: 'profile',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 13\n    }, this),\n    label: '个人资料'\n  }, {\n    key: 'settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this),\n    label: '偏好设置'\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 13\n    }, this),\n    label: '退出登录'\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n  const handleUserMenuClick = ({\n    key\n  }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AntLayout, {\n    className: \"layout-container\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"layout-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              marginRight: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1890ff',\n              fontSize: '20px',\n              fontWeight: 'bold'\n            },\n            children: \"NovelCraft\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            menu: {\n              items: userMenuItems,\n              onClick: handleUserMenuClick\n            },\n            placement: \"bottomRight\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              style: {\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u7528\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AntLayout, {\n      className: \"layout-content\",\n      children: [/*#__PURE__*/_jsxDEV(Sider, {\n        className: \"layout-sider\",\n        collapsed: collapsed,\n        width: 240,\n        collapsedWidth: 80,\n        theme: \"light\",\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          mode: \"inline\",\n          selectedKeys: [location.pathname],\n          items: menuItems,\n          onClick: handleMenuClick,\n          style: {\n            height: '100%',\n            borderRight: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        className: \"layout-main\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"t/KUUEFbq3mghKN9ZxnFsjygAfg=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Layout", "AntLayout", "<PERSON><PERSON>", "Avatar", "Dropdown", "<PERSON><PERSON>", "Space", "Collapse", "useNavigate", "useLocation", "DashboardOutlined", "ProjectOutlined", "UserOutlined", "TeamOutlined", "BookOutlined", "FileTextOutlined", "GlobalOutlined", "ThunderboltOutlined", "ClockCircleOutlined", "ShareAltOutlined", "RobotOutlined", "SettingOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "LogoutOutlined", "BellOutlined", "EnvironmentOutlined", "EyeOutlined", "ShoppingOutlined", "HeartOutlined", "CompassOutlined", "StarOutlined", "CrownOutlined", "BankOutlined", "DownOutlined", "RightOutlined", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "Panel", "children", "_s", "collapsed", "setCollapsed", "contentCollapsed", "setContentCollapsed", "settingsCollapsed", "setSettingsCollapsed", "navigate", "location", "getProjectId", "pathParts", "pathname", "split", "projectId", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "type", "userMenuItems", "handleMenuClick", "handleUserMenuClick", "console", "log", "className", "style", "display", "alignItems", "justifyContent", "onClick", "marginRight", "margin", "color", "fontSize", "fontWeight", "menu", "items", "placement", "cursor", "width", "collapsedWidth", "theme", "mode", "<PERSON><PERSON><PERSON><PERSON>", "height", "borderRight", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space, Collapse } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  DashboardOutlined,\n  ProjectOutlined,\n  UserOutlined,\n  TeamOutlined,\n  BookOutlined,\n  FileTextOutlined,\n  GlobalOutlined,\n  ThunderboltOutlined,\n  ClockCircleOutlined,\n  ShareAltOutlined,\n  RobotOutlined,\n  SettingOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  LogoutOutlined,\n  BellOutlined,\n  EnvironmentOutlined,\n  EyeOutlined,\n  ShoppingOutlined,\n  HeartOutlined,\n  CompassOutlined,\n  StarOutlined,\n  CrownOutlined,\n  BankOutlined,\n  DownOutlined,\n  RightOutlined\n} from '@ant-design/icons';\n\nconst { Header, Sider, Content } = AntLayout;\nconst { Panel } = Collapse;\n\nconst Layout = ({ children }) => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [contentCollapsed, setContentCollapsed] = useState(false);\n  const [settingsCollapsed, setSettingsCollapsed] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取当前项目ID（如果在项目页面中）\n  const getProjectId = () => {\n    const pathParts = location.pathname.split('/');\n    if (pathParts[1] === 'projects' && pathParts[2]) {\n      return pathParts[2];\n    }\n    return null;\n  };\n\n  const projectId = getProjectId();\n\n  // 菜单项配置\n  const menuItems = [\n    {\n      key: '/',\n      icon: <DashboardOutlined />,\n      label: '仪表盘',\n    },\n    {\n      key: '/projects',\n      icon: <ProjectOutlined />,\n      label: '项目管理',\n    },\n    // 只有在项目页面中才显示内容管理菜单\n    ...(projectId ? [\n      {\n        key: 'content',\n        label: '内容管理',\n        type: 'group',\n      },\n      {\n        key: `/projects/${projectId}/characters`,\n        icon: <UserOutlined />,\n        label: '人物管理',\n      },\n      {\n        key: `/projects/${projectId}/factions`,\n        icon: <TeamOutlined />,\n        label: '势力管理',\n      },\n      {\n        key: `/projects/${projectId}/plots`,\n        icon: <BookOutlined />,\n        label: '剧情管理',\n      },\n      {\n        key: `/projects/${projectId}/volumes`,\n        icon: <FileTextOutlined />,\n        label: '卷宗管理',\n      },\n      {\n        key: `/projects/${projectId}/resource-distribution`,\n        icon: <EnvironmentOutlined />,\n        label: '资源分布',\n      },\n      {\n        key: `/projects/${projectId}/race-distribution`,\n        icon: <TeamOutlined />,\n        label: '种族分布',\n      },\n      {\n        key: `/projects/${projectId}/secret-realms`,\n        icon: <EyeOutlined />,\n        label: '秘境分布',\n      },\n      {\n        key: 'settings',\n        label: '设定管理',\n        type: 'group',\n      },\n      {\n        key: `/projects/${projectId}/world-settings`,\n        icon: <GlobalOutlined />,\n        label: '世界设定',\n      },\n      {\n        key: `/projects/${projectId}/cultivation-systems`,\n        icon: <ThunderboltOutlined />,\n        label: '修炼体系',\n      },\n      {\n        key: `/projects/${projectId}/equipment-systems`,\n        icon: <ShoppingOutlined />,\n        label: '装备体系',\n      },\n      {\n        key: `/projects/${projectId}/pet-systems`,\n        icon: <HeartOutlined />,\n        label: '宠物体系',\n      },\n      {\n        key: `/projects/${projectId}/map-structure`,\n        icon: <CompassOutlined />,\n        label: '地图结构',\n      },\n      {\n        key: `/projects/${projectId}/dimension-structure`,\n        icon: <StarOutlined />,\n        label: '维度结构',\n      },\n      {\n        key: `/projects/${projectId}/spiritual-treasure-systems`,\n        icon: <CrownOutlined />,\n        label: '灵宝体系',\n      },\n      {\n        key: `/projects/${projectId}/civilian-systems`,\n        icon: <TeamOutlined />,\n        label: '生民体系',\n      },\n      {\n        key: `/projects/${projectId}/judicial-systems`,\n        icon: <BankOutlined />,\n        label: '司法体系',\n      },\n      {\n        key: `/projects/${projectId}/profession-systems`,\n        icon: <UserOutlined />,\n        label: '职业体系',\n      },\n      {\n        key: `/projects/${projectId}/timeline`,\n        icon: <ClockCircleOutlined />,\n        label: '时间线',\n      },\n      {\n        key: `/projects/${projectId}/relations`,\n        icon: <ShareAltOutlined />,\n        label: '关系网络',\n      },\n    ] : []),\n    {\n      key: 'tools',\n      label: '工具',\n      type: 'group',\n    },\n    {\n      key: '/ai-assistant',\n      icon: <RobotOutlined />,\n      label: 'AI助手',\n    },\n    {\n      key: '/ai-test',\n      icon: <RobotOutlined />,\n      label: 'AI测试',\n    },\n    {\n      key: '/settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '偏好设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    navigate(key);\n  };\n\n  const handleUserMenuClick = ({ key }) => {\n    switch (key) {\n      case 'profile':\n        navigate('/profile');\n        break;\n      case 'settings':\n        navigate('/settings');\n        break;\n      case 'logout':\n        // 处理退出登录\n        console.log('退出登录');\n        break;\n      default:\n        break;\n    }\n  };\n\n  return (\n    <AntLayout className=\"layout-container\">\n      <Header className=\"layout-header\">\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ display: 'flex', alignItems: 'center' }}>\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{ marginRight: 16 }}\n            />\n            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>\n              NovelCraft\n            </h1>\n          </div>\n\n          <Space>\n            <Button type=\"text\" icon={<BellOutlined />} />\n            <Dropdown\n              menu={{\n                items: userMenuItems,\n                onClick: handleUserMenuClick,\n              }}\n              placement=\"bottomRight\"\n            >\n              <Space style={{ cursor: 'pointer' }}>\n                <Avatar icon={<UserOutlined />} />\n                <span>用户</span>\n              </Space>\n            </Dropdown>\n          </Space>\n        </div>\n      </Header>\n\n      <AntLayout className=\"layout-content\">\n        <Sider\n          className=\"layout-sider\"\n          collapsed={collapsed}\n          width={240}\n          collapsedWidth={80}\n          theme=\"light\"\n        >\n          <Menu\n            mode=\"inline\"\n            selectedKeys={[location.pathname]}\n            items={menuItems}\n            onClick={handleMenuClick}\n            style={{ height: '100%', borderRight: 0 }}\n          />\n        </Sider>\n\n        <Content className=\"layout-main\">\n          {children}\n        </Content>\n      </AntLayout>\n    </AntLayout>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,IAAIC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,MAAM;AAC3F,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,aAAa,QACR,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGvC,SAAS;AAC5C,MAAM;EAAEwC;AAAM,CAAC,GAAGlC,QAAQ;AAE1B,MAAMP,MAAM,GAAGA,CAAC;EAAE0C;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMmD,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM2C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAGF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,IAAIA,SAAS,CAAC,CAAC,CAAC,EAAE;MAC/C,OAAOA,SAAS,CAAC,CAAC,CAAC;IACrB;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,SAAS,GAAGJ,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAMK,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAEtB,OAAA,CAAC3B,iBAAiB;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEtB,OAAA,CAAC1B,eAAe;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC;EACD;EACA,IAAIR,SAAS,GAAG,CACd;IACEE,GAAG,EAAE,SAAS;IACdM,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,aAAaF,SAAS,aAAa;IACxCG,IAAI,eAAEtB,OAAA,CAACzB,YAAY;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,WAAW;IACtCG,IAAI,eAAEtB,OAAA,CAACxB,YAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,QAAQ;IACnCG,IAAI,eAAEtB,OAAA,CAACvB,YAAY;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,UAAU;IACrCG,IAAI,eAAEtB,OAAA,CAACtB,gBAAgB;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,wBAAwB;IACnDG,IAAI,eAAEtB,OAAA,CAACX,mBAAmB;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,oBAAoB;IAC/CG,IAAI,eAAEtB,OAAA,CAACxB,YAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,gBAAgB;IAC3CG,IAAI,eAAEtB,OAAA,CAACV,WAAW;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfM,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,aAAaF,SAAS,iBAAiB;IAC5CG,IAAI,eAAEtB,OAAA,CAACrB,cAAc;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,sBAAsB;IACjDG,IAAI,eAAEtB,OAAA,CAACpB,mBAAmB;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,oBAAoB;IAC/CG,IAAI,eAAEtB,OAAA,CAACT,gBAAgB;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,cAAc;IACzCG,IAAI,eAAEtB,OAAA,CAACR,aAAa;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,gBAAgB;IAC3CG,IAAI,eAAEtB,OAAA,CAACP,eAAe;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,sBAAsB;IACjDG,IAAI,eAAEtB,OAAA,CAACN,YAAY;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,6BAA6B;IACxDG,IAAI,eAAEtB,OAAA,CAACL,aAAa;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,mBAAmB;IAC9CG,IAAI,eAAEtB,OAAA,CAACxB,YAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,mBAAmB;IAC9CG,IAAI,eAAEtB,OAAA,CAACJ,YAAY;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,qBAAqB;IAChDG,IAAI,eAAEtB,OAAA,CAACzB,YAAY;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,WAAW;IACtCG,IAAI,eAAEtB,OAAA,CAACnB,mBAAmB;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAaF,SAAS,YAAY;IACvCG,IAAI,eAAEtB,OAAA,CAAClB,gBAAgB;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF,GAAG,EAAE,CAAC,EACP;IACEN,GAAG,EAAE,OAAO;IACZM,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAEtB,OAAA,CAACjB,aAAa;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEtB,OAAA,CAACjB,aAAa;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEtB,OAAA,CAAChB,eAAe;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAME,aAAa,GAAG,CACpB;IACER,GAAG,EAAE,SAAS;IACdC,IAAI,eAAEtB,OAAA,CAACzB,YAAY;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEtB,OAAA,CAAChB,eAAe;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEtB,OAAA,CAACb,cAAc;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMG,eAAe,GAAGA,CAAC;IAAET;EAAI,CAAC,KAAK;IACnCR,QAAQ,CAACQ,GAAG,CAAC;EACf,CAAC;EAED,MAAMU,mBAAmB,GAAGA,CAAC;IAAEV;EAAI,CAAC,KAAK;IACvC,QAAQA,GAAG;MACT,KAAK,SAAS;QACZR,QAAQ,CAAC,UAAU,CAAC;QACpB;MACF,KAAK,UAAU;QACbA,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF,KAAK,QAAQ;QACX;QACAmB,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;QACnB;MACF;QACE;IACJ;EACF,CAAC;EAED,oBACEjC,OAAA,CAACpC,SAAS;IAACsE,SAAS,EAAC,kBAAkB;IAAA7B,QAAA,gBACrCL,OAAA,CAACC,MAAM;MAACiC,SAAS,EAAC,eAAe;MAAA7B,QAAA,eAC/BL,OAAA;QAAKmC,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAjC,QAAA,gBACrFL,OAAA;UAAKmC,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAhC,QAAA,gBACpDL,OAAA,CAAChC,MAAM;YACL4D,IAAI,EAAC,MAAM;YACXN,IAAI,EAAEf,SAAS,gBAAGP,OAAA,CAACd,kBAAkB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG1B,OAAA,CAACf,gBAAgB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEa,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC,CAACD,SAAS,CAAE;YACxC4B,KAAK,EAAE;cAAEK,WAAW,EAAE;YAAG;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF1B,OAAA;YAAImC,KAAK,EAAE;cAAEM,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAvC,QAAA,EAAC;UAElF;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN1B,OAAA,CAAC/B,KAAK;UAAAoC,QAAA,gBACJL,OAAA,CAAChC,MAAM;YAAC4D,IAAI,EAAC,MAAM;YAACN,IAAI,eAAEtB,OAAA,CAACZ,YAAY;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9C1B,OAAA,CAACjC,QAAQ;YACP8E,IAAI,EAAE;cACJC,KAAK,EAAEjB,aAAa;cACpBU,OAAO,EAAER;YACX,CAAE;YACFgB,SAAS,EAAC,aAAa;YAAA1C,QAAA,eAEvBL,OAAA,CAAC/B,KAAK;cAACkE,KAAK,EAAE;gBAAEa,MAAM,EAAE;cAAU,CAAE;cAAA3C,QAAA,gBAClCL,OAAA,CAAClC,MAAM;gBAACwD,IAAI,eAAEtB,OAAA,CAACzB,YAAY;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClC1B,OAAA;gBAAAK,QAAA,EAAM;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET1B,OAAA,CAACpC,SAAS;MAACsE,SAAS,EAAC,gBAAgB;MAAA7B,QAAA,gBACnCL,OAAA,CAACE,KAAK;QACJgC,SAAS,EAAC,cAAc;QACxB3B,SAAS,EAAEA,SAAU;QACrB0C,KAAK,EAAE,GAAI;QACXC,cAAc,EAAE,EAAG;QACnBC,KAAK,EAAC,OAAO;QAAA9C,QAAA,eAEbL,OAAA,CAACnC,IAAI;UACHuF,IAAI,EAAC,QAAQ;UACbC,YAAY,EAAE,CAACvC,QAAQ,CAACG,QAAQ,CAAE;UAClC6B,KAAK,EAAE1B,SAAU;UACjBmB,OAAO,EAAET,eAAgB;UACzBK,KAAK,EAAE;YAAEmB,MAAM,EAAE,MAAM;YAAEC,WAAW,EAAE;UAAE;QAAE;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAER1B,OAAA,CAACG,OAAO;QAAC+B,SAAS,EAAC,aAAa;QAAA7B,QAAA,EAC7BA;MAAQ;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACpB,EAAA,CApQI3C,MAAM;EAAA,QAIOQ,WAAW,EACXC,WAAW;AAAA;AAAAoF,EAAA,GALxB7F,MAAM;AAsQZ,eAAeA,MAAM;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}