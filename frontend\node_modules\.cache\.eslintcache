[{"D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js": "1", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js": "2", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js": "3", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js": "4", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js": "5", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js": "6", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js": "7", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js": "8", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js": "9", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js": "10", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js": "11", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js": "12", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js": "13", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js": "14", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js": "15", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js": "16", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js": "17", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js": "18", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js": "19", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js": "20", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js": "21", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js": "22", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js": "23", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js": "24", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js": "25", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js": "26", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterDetail.js": "27", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeList.js": "28", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CivilianSystems.js": "29", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\JudicialSystems.js": "30", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProfessionSystems.js": "31"}, {"size": 254, "mtime": 1748329246695, "results": "32", "hashOfConfig": "33"}, {"size": 4782, "mtime": 1748496161526, "results": "34", "hashOfConfig": "33"}, {"size": 10227, "mtime": 1748505288287, "results": "35", "hashOfConfig": "33"}, {"size": 9142, "mtime": 1748488507886, "results": "36", "hashOfConfig": "33"}, {"size": 18761, "mtime": 1748351266251, "results": "37", "hashOfConfig": "33"}, {"size": 12041, "mtime": 1748503247673, "results": "38", "hashOfConfig": "33"}, {"size": 9758, "mtime": 1748329098388, "results": "39", "hashOfConfig": "33"}, {"size": 20451, "mtime": 1748353392933, "results": "40", "hashOfConfig": "33"}, {"size": 29827, "mtime": 1748483630000, "results": "41", "hashOfConfig": "33"}, {"size": 24215, "mtime": 1748352877767, "results": "42", "hashOfConfig": "33"}, {"size": 23256, "mtime": 1748352524186, "results": "43", "hashOfConfig": "33"}, {"size": 22880, "mtime": 1748353123046, "results": "44", "hashOfConfig": "33"}, {"size": 44589, "mtime": 1748502631872, "results": "45", "hashOfConfig": "33"}, {"size": 19283, "mtime": 1748352994687, "results": "46", "hashOfConfig": "33"}, {"size": 4503, "mtime": 1748341004213, "results": "47", "hashOfConfig": "33"}, {"size": 26386, "mtime": 1748502592952, "results": "48", "hashOfConfig": "33"}, {"size": 5825, "mtime": 1748502680240, "results": "49", "hashOfConfig": "33"}, {"size": 8260, "mtime": 1748502719538, "results": "50", "hashOfConfig": "33"}, {"size": 16915, "mtime": 1748487765369, "results": "51", "hashOfConfig": "33"}, {"size": 18146, "mtime": 1748487204858, "results": "52", "hashOfConfig": "33"}, {"size": 15353, "mtime": 1748486959232, "results": "53", "hashOfConfig": "33"}, {"size": 15084, "mtime": 1748486881151, "results": "54", "hashOfConfig": "33"}, {"size": 14317, "mtime": 1748487034064, "results": "55", "hashOfConfig": "33"}, {"size": 17842, "mtime": 1748487380513, "results": "56", "hashOfConfig": "33"}, {"size": 21003, "mtime": 1748487480069, "results": "57", "hashOfConfig": "33"}, {"size": 19068, "mtime": 1748487295697, "results": "58", "hashOfConfig": "33"}, {"size": 9772, "mtime": 1748488625998, "results": "59", "hashOfConfig": "33"}, {"size": 31513, "mtime": 1748489536415, "results": "60", "hashOfConfig": "33"}, {"size": 10799, "mtime": 1748496033772, "results": "61", "hashOfConfig": "33"}, {"size": 10233, "mtime": 1748496079485, "results": "62", "hashOfConfig": "33"}, {"size": 9052, "mtime": 1748496118220, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js", ["157", "158", "159"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js", ["160", "161", "162", "163"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js", ["164", "165", "166", "167"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js", ["168"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js", ["169"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js", ["170"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js", ["171", "172"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js", ["173"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js", ["174", "175"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js", ["176", "177", "178"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js", ["179", "180"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js", ["181"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js", ["182", "183"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js", ["184"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js", ["185", "186", "187"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js", ["188", "189"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js", ["190"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js", ["191", "192"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js", ["193", "194"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js", ["195"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js", ["196"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js", ["197", "198"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterDetail.js", ["199"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeList.js", ["200", "201", "202", "203", "204", "205", "206", "207", "208"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CivilianSystems.js", ["209", "210", "211", "212", "213"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\JudicialSystems.js", ["214", "215", "216"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProfessionSystems.js", ["217", "218", "219", "220", "221"], [], {"ruleId": "222", "severity": 1, "message": "223", "line": 29, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 29, "endColumn": 15}, {"ruleId": "222", "severity": 1, "message": "226", "line": 30, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 30, "endColumn": 16}, {"ruleId": "222", "severity": 1, "message": "227", "line": 216, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 216, "endColumn": 32}, {"ruleId": "222", "severity": 1, "message": "228", "line": 1, "column": 27, "nodeType": "224", "messageId": "225", "endLine": 1, "endColumn": 36}, {"ruleId": "222", "severity": 1, "message": "229", "line": 18, "column": 17, "nodeType": "224", "messageId": "225", "endLine": 18, "endColumn": 25}, {"ruleId": "222", "severity": 1, "message": "230", "line": 25, "column": 26, "nodeType": "224", "messageId": "225", "endLine": 25, "endColumn": 43}, {"ruleId": "222", "severity": 1, "message": "231", "line": 55, "column": 28, "nodeType": "224", "messageId": "225", "endLine": 55, "endColumn": 47}, {"ruleId": "222", "severity": 1, "message": "232", "line": 21, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 21, "endColumn": 9}, {"ruleId": "222", "severity": 1, "message": "233", "line": 34, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 34, "endColumn": 17}, {"ruleId": "222", "severity": 1, "message": "234", "line": 37, "column": 22, "nodeType": "224", "messageId": "225", "endLine": 37, "endColumn": 31}, {"ruleId": "235", "severity": 1, "message": "236", "line": 107, "column": 6, "nodeType": "237", "endLine": 107, "endColumn": 8, "suggestions": "238"}, {"ruleId": "235", "severity": 1, "message": "239", "line": 123, "column": 6, "nodeType": "237", "endLine": 123, "endColumn": 8, "suggestions": "240"}, {"ruleId": "235", "severity": 1, "message": "241", "line": 55, "column": 6, "nodeType": "237", "endLine": 55, "endColumn": 10, "suggestions": "242"}, {"ruleId": "235", "severity": 1, "message": "243", "line": 112, "column": 6, "nodeType": "237", "endLine": 112, "endColumn": 8, "suggestions": "244"}, {"ruleId": "222", "severity": 1, "message": "245", "line": 19, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 19, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "246", "line": 116, "column": 6, "nodeType": "237", "endLine": 116, "endColumn": 8, "suggestions": "247"}, {"ruleId": "235", "severity": 1, "message": "248", "line": 131, "column": 6, "nodeType": "237", "endLine": 131, "endColumn": 8, "suggestions": "249"}, {"ruleId": "235", "severity": 1, "message": "250", "line": 109, "column": 6, "nodeType": "237", "endLine": 109, "endColumn": 8, "suggestions": "251"}, {"ruleId": "222", "severity": 1, "message": "252", "line": 344, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 344, "endColumn": 23}, {"ruleId": "222", "severity": 1, "message": "253", "line": 22, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 22, "endColumn": 10}, {"ruleId": "222", "severity": 1, "message": "254", "line": 32, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 32, "endColumn": 18}, {"ruleId": "235", "severity": 1, "message": "255", "line": 148, "column": 6, "nodeType": "237", "endLine": 148, "endColumn": 8, "suggestions": "256"}, {"ruleId": "222", "severity": 1, "message": "253", "line": 20, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 20, "endColumn": 10}, {"ruleId": "235", "severity": 1, "message": "257", "line": 115, "column": 6, "nodeType": "237", "endLine": 115, "endColumn": 8, "suggestions": "258"}, {"ruleId": "222", "severity": 1, "message": "259", "line": 23, "column": 16, "nodeType": "224", "messageId": "225", "endLine": 23, "endColumn": 20}, {"ruleId": "222", "severity": 1, "message": "260", "line": 108, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 108, "endColumn": 19}, {"ruleId": "235", "severity": 1, "message": "261", "line": 371, "column": 6, "nodeType": "237", "endLine": 371, "endColumn": 8, "suggestions": "262"}, {"ruleId": "235", "severity": 1, "message": "263", "line": 133, "column": 6, "nodeType": "237", "endLine": 133, "endColumn": 8, "suggestions": "264"}, {"ruleId": "222", "severity": 1, "message": "265", "line": 22, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 22, "endColumn": 11}, {"ruleId": "222", "severity": 1, "message": "266", "line": 33, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 33, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "267", "line": 102, "column": 6, "nodeType": "237", "endLine": 102, "endColumn": 17, "suggestions": "268"}, {"ruleId": "222", "severity": 1, "message": "269", "line": 31, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 31, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "270", "line": 112, "column": 6, "nodeType": "237", "endLine": 112, "endColumn": 17, "suggestions": "271"}, {"ruleId": "235", "severity": 1, "message": "272", "line": 87, "column": 6, "nodeType": "237", "endLine": 87, "endColumn": 17, "suggestions": "273"}, {"ruleId": "222", "severity": 1, "message": "265", "line": 21, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 21, "endColumn": 11}, {"ruleId": "235", "severity": 1, "message": "274", "line": 81, "column": 6, "nodeType": "237", "endLine": 81, "endColumn": 17, "suggestions": "275"}, {"ruleId": "222", "severity": 1, "message": "265", "line": 22, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 22, "endColumn": 11}, {"ruleId": "235", "severity": 1, "message": "276", "line": 87, "column": 6, "nodeType": "237", "endLine": 87, "endColumn": 17, "suggestions": "277"}, {"ruleId": "235", "severity": 1, "message": "278", "line": 112, "column": 6, "nodeType": "237", "endLine": 112, "endColumn": 17, "suggestions": "279"}, {"ruleId": "235", "severity": 1, "message": "280", "line": 140, "column": 6, "nodeType": "237", "endLine": 140, "endColumn": 17, "suggestions": "281"}, {"ruleId": "222", "severity": 1, "message": "282", "line": 31, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 31, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "283", "line": 106, "column": 6, "nodeType": "237", "endLine": 106, "endColumn": 17, "suggestions": "284"}, {"ruleId": "222", "severity": 1, "message": "253", "line": 16, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 16, "endColumn": 10}, {"ruleId": "222", "severity": 1, "message": "285", "line": 25, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 25, "endColumn": 8}, {"ruleId": "222", "severity": 1, "message": "286", "line": 35, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 35, "endColumn": 22}, {"ruleId": "222", "severity": 1, "message": "287", "line": 38, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 38, "endColumn": 22}, {"ruleId": "222", "severity": 1, "message": "288", "line": 39, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 39, "endColumn": 19}, {"ruleId": "222", "severity": 1, "message": "289", "line": 45, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 45, "endColumn": 14}, {"ruleId": "222", "severity": 1, "message": "290", "line": 46, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 46, "endColumn": 16}, {"ruleId": "222", "severity": 1, "message": "291", "line": 60, "column": 10, "nodeType": "224", "messageId": "225", "endLine": 60, "endColumn": 26}, {"ruleId": "235", "severity": 1, "message": "292", "line": 183, "column": 6, "nodeType": "237", "endLine": 183, "endColumn": 8, "suggestions": "293"}, {"ruleId": "222", "severity": 1, "message": "294", "line": 188, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 188, "endColumn": 26}, {"ruleId": "222", "severity": 1, "message": "288", "line": 29, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 29, "endColumn": 19}, {"ruleId": "222", "severity": 1, "message": "295", "line": 35, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 35, "endColumn": 15}, {"ruleId": "222", "severity": 1, "message": "296", "line": 47, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 47, "endColumn": 27}, {"ruleId": "222", "severity": 1, "message": "297", "line": 61, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 61, "endColumn": 25}, {"ruleId": "235", "severity": 1, "message": "298", "line": 74, "column": 6, "nodeType": "237", "endLine": 74, "endColumn": 17, "suggestions": "299"}, {"ruleId": "222", "severity": 1, "message": "300", "line": 19, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 19, "endColumn": 12}, {"ruleId": "222", "severity": 1, "message": "301", "line": 29, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 29, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "298", "line": 64, "column": 6, "nodeType": "237", "endLine": 64, "endColumn": 17, "suggestions": "302"}, {"ruleId": "222", "severity": 1, "message": "300", "line": 19, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 19, "endColumn": 12}, {"ruleId": "222", "severity": 1, "message": "303", "line": 29, "column": 3, "nodeType": "224", "messageId": "225", "endLine": 29, "endColumn": 15}, {"ruleId": "222", "severity": 1, "message": "295", "line": 35, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 35, "endColumn": 15}, {"ruleId": "222", "severity": 1, "message": "304", "line": 47, "column": 9, "nodeType": "224", "messageId": "225", "endLine": 47, "endColumn": 34}, {"ruleId": "235", "severity": 1, "message": "298", "line": 64, "column": 6, "nodeType": "237", "endLine": 64, "endColumn": 17, "suggestions": "305"}, "no-unused-vars", "'DownOutlined' is defined but never used.", "Identifier", "unusedVar", "'RightOutlined' is defined but never used.", "'handleCollapseMenuClick' is assigned a value but never used.", "'useEffect' is defined but never used.", "'setStats' is assigned a value but never used.", "'setRecentProjects' is assigned a value but never used.", "'setRecentActivities' is assigned a value but never used.", "'Upload' is defined but never used.", "'UploadOutlined' is defined but never used.", "'Paragraph' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mockCharacters'. Either include it or remove the dependency array.", "ArrayExpression", ["306"], "React Hook useEffect has a missing dependency: 'loadProjects'. Either include it or remove the dependency array.", ["307"], "React Hook useEffect has a missing dependency: 'loadProject'. Either include it or remove the dependency array.", ["308"], "React Hook useEffect has a missing dependency: 'mockFactions'. Either include it or remove the dependency array.", ["309"], "'Descriptions' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockSettings'. Either include it or remove the dependency array.", ["310"], "React Hook useEffect has a missing dependency: 'mockSystems'. Either include it or remove the dependency array.", ["311"], "React Hook useEffect has a missing dependency: 'mockPlots'. Either include it or remove the dependency array.", ["312"], "'completedPlots' is assigned a value but never used.", "'Divider' is defined but never used.", "'HistoryOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockEvents'. Either include it or remove the dependency array.", ["313"], "React Hook useEffect has a missing dependency: 'mockRelations'. Either include it or remove the dependency array.", ["314"], "'Text' is assigned a value but never used.", "'providers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAIInfo'. Either include it or remove the dependency array.", ["315"], "React Hook useEffect has a missing dependency: 'testAllAPIs'. Either include it or remove the dependency array.", ["316"], "'Progress' is defined but never used.", "'StarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEquipment'. Either include it or remove the dependency array.", ["317"], "'ShieldOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadPets'. Either include it or remove the dependency array.", ["318"], "React Hook useEffect has a missing dependency: 'loadRaces'. Either include it or remove the dependency array.", ["319"], "React Hook useEffect has a missing dependency: 'loadResources'. Either include it or remove the dependency array.", ["320"], "React Hook useEffect has a missing dependency: 'loadRealms'. Either include it or remove the dependency array.", ["321"], "React Hook useEffect has a missing dependency: 'loadDimensions'. Either include it or remove the dependency array.", ["322"], "React Hook useEffect has a missing dependency: 'loadTreasures'. Either include it or remove the dependency array.", ["323"], "'ShopOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLocations'. Either include it or remove the dependency array.", ["324"], "'Empty' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'OrderedListOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'Panel' is assigned a value but never used.", "'TabPane' is assigned a value but never used.", "'selectedVolumeId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'mockChapters' and 'mockVolumes'. Either include them or remove the dependency array.", ["325"], "'completedChapters' is assigned a value but never used.", "'Option' is assigned a value but never used.", "'socialClassOptions' is assigned a value but never used.", "'lifestyleOptions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSystems'. Either include it or remove the dependency array.", ["326"], "'Statistic' is defined but never used.", "'SafetyOutlined' is defined but never used.", ["327"], "'ToolOutlined' is defined but never used.", "'professionCategoryOptions' is assigned a value but never used.", ["328"], {"desc": "329", "fix": "330"}, {"desc": "331", "fix": "332"}, {"desc": "333", "fix": "334"}, {"desc": "335", "fix": "336"}, {"desc": "337", "fix": "338"}, {"desc": "339", "fix": "340"}, {"desc": "341", "fix": "342"}, {"desc": "343", "fix": "344"}, {"desc": "345", "fix": "346"}, {"desc": "347", "fix": "348"}, {"desc": "349", "fix": "350"}, {"desc": "351", "fix": "352"}, {"desc": "353", "fix": "354"}, {"desc": "355", "fix": "356"}, {"desc": "357", "fix": "358"}, {"desc": "359", "fix": "360"}, {"desc": "361", "fix": "362"}, {"desc": "363", "fix": "364"}, {"desc": "365", "fix": "366"}, {"desc": "367", "fix": "368"}, {"desc": "369", "fix": "370"}, {"desc": "369", "fix": "371"}, {"desc": "369", "fix": "372"}, "Update the dependencies array to be: [mockCharacters]", {"range": "373", "text": "374"}, "Update the dependencies array to be: [loadProjects]", {"range": "375", "text": "376"}, "Update the dependencies array to be: [id, loadProject]", {"range": "377", "text": "378"}, "Update the dependencies array to be: [mockFactions]", {"range": "379", "text": "380"}, "Update the dependencies array to be: [mockSettings]", {"range": "381", "text": "382"}, "Update the dependencies array to be: [mockSystems]", {"range": "383", "text": "384"}, "Update the dependencies array to be: [mockPlots]", {"range": "385", "text": "386"}, "Update the dependencies array to be: [mockEvents]", {"range": "387", "text": "388"}, "Update the dependencies array to be: [mockRelations]", {"range": "389", "text": "390"}, "Update the dependencies array to be: [fetchAIInfo]", {"range": "391", "text": "392"}, "Update the dependencies array to be: [testAllAPIs]", {"range": "393", "text": "394"}, "Update the dependencies array to be: [loadEquipment, projectId]", {"range": "395", "text": "396"}, "Update the dependencies array to be: [loadPets, projectId]", {"range": "397", "text": "398"}, "Update the dependencies array to be: [loadRaces, projectId]", {"range": "399", "text": "400"}, "Update the dependencies array to be: [loadResources, projectId]", {"range": "401", "text": "402"}, "Update the dependencies array to be: [loadRealms, projectId]", {"range": "403", "text": "404"}, "Update the dependencies array to be: [loadDimensions, projectId]", {"range": "405", "text": "406"}, "Update the dependencies array to be: [loadTreasures, projectId]", {"range": "407", "text": "408"}, "Update the dependencies array to be: [loadLocations, projectId]", {"range": "409", "text": "410"}, "Update the dependencies array to be: [mockChapters, mockVolumes]", {"range": "411", "text": "412"}, "Update the dependencies array to be: [fetchSystems, projectId]", {"range": "413", "text": "414"}, {"range": "415", "text": "414"}, {"range": "416", "text": "414"}, [2366, 2368], "[mockCharacters]", [2501, 2503], "[loadProjects]", [1073, 1077], "[id, loadProject]", [2433, 2435], "[mockFactions]", [2658, 2660], "[mockSettings]", [4240, 4242], "[mockSystems]", [2508, 2510], "[mockPlots]", [3453, 3455], "[mockEvents]", [2672, 2674], "[mockRelations]", [9568, 9570], "[fetchAIInfo]", [3445, 3447], "[testAllAPIs]", [2098, 2109], "[loadEquipment, projectId]", [2267, 2278], "[loadPets, projectId]", [1775, 1786], "[loadRaces, projectId]", [1716, 1727], "[loadResources, projectId]", [1850, 1861], "[loadRealms, projectId]", [2509, 2520], "[loadDimensions, projectId]", [3146, 3157], "[loadTreasures, projectId]", [2363, 2374], "[loadLocations, projectId]", [4425, 4427], "[mockChapters, mockVolumes]", [1755, 1766], "[fetchSystems, projectId]", [1396, 1407], [1500, 1511]]