/* NovelCraft 应用样式 */

.App {
  height: 100vh;
  overflow: hidden;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 布局样式 */
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.layout-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.layout-main {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f5f5f5;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.page-description {
  color: #8c8c8c;
  margin-top: 8px;
  margin-bottom: 0;
}

/* 卡片样式 */
.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.card-content {
  padding: 24px;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stats-value {
  font-size: 32px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.stats-label {
  color: #8c8c8c;
  font-size: 14px;
}

/* 表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 编辑器样式 */
.editor-container {
  height: 500px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-content {
  height: calc(100% - 41px);
}

/* 侧边栏样式 */
.sidebar-section {
  margin-bottom: 24px;
}

.sidebar-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
  padding: 0 16px;
}

.sidebar-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.sidebar-item:hover {
  background-color: #f5f5f5;
}

.sidebar-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-main {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 48px 24px;
  color: #8c8c8c;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 16px;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* AI配置面板样式 */
.provider-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.provider-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.provider-card.active {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.provider-card .ant-card-body {
  padding: 16px 12px;
}

/* 可折叠菜单样式 */
.ant-collapse-ghost > .ant-collapse-item {
  border: none;
}

.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header {
  padding: 8px 16px;
  background: transparent;
  border: none;
  font-weight: 500;
  color: #595959;
}

.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header:hover {
  background-color: #f5f5f5;
}

.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
  padding: 0;
}

.ant-collapse-ghost .ant-collapse-arrow {
  right: 8px !important;
  color: #8c8c8c;
}

/* 菜单项样式优化 */
.ant-menu-inline .ant-menu-item {
  margin: 0;
  border-radius: 0;
  height: 40px;
  line-height: 40px;
}

.ant-menu-inline .ant-menu-item:hover {
  background-color: #f0f0f0;
}

.ant-menu-inline .ant-menu-item-selected {
  background-color: #e6f7ff;
  border-right: 3px solid #1890ff;
  color: #1890ff;
}

.ant-menu-inline .ant-menu-item-selected::after {
  border-right: 3px solid #1890ff;
}

/* 侧边栏折叠状态下的样式 */
.ant-layout-sider-collapsed .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header {
  padding: 8px 12px;
  text-align: center;
}

.ant-layout-sider-collapsed .ant-collapse-ghost .ant-collapse-arrow {
  display: none;
}

.ant-layout-sider-collapsed .ant-menu-inline .ant-menu-item {
  padding: 0 !important;
  text-align: center;
}

/* 工具菜单分隔线 */
.tools-divider {
  border-top: 1px solid #f0f0f0;
  margin: 8px 0;
  padding-top: 8px;
}

.tools-title {
  padding: 8px 16px;
  font-size: 12px;
  color: #999;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
