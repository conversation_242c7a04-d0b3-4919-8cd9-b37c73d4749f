{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\components\\\\AIConfigPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Form, Input, Select, Button, Space, Tag, Alert, Row, Col, Divider, Typography, Switch, InputNumber, message, Tooltip } from 'antd';\nimport { RobotOutlined, CheckCircleOutlined, ExclamationCircleOutlined, ReloadOutlined, SaveOutlined, EyeInvisibleOutlined, EyeTwoTone, InfoCircleOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\n\n// AI提供商配置信息\nconst AI_PROVIDERS = {\n  openai: {\n    name: 'OpenAI',\n    icon: '🤖',\n    color: '#10a37f',\n    description: '最先进的GPT模型',\n    models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  claude: {\n    name: 'Claude',\n    icon: '🧠',\n    color: '#ff6b35',\n    description: 'Anthropic的Claude模型',\n    models: ['claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  zhipu: {\n    name: '智谱AI',\n    icon: '🇨🇳',\n    color: '#1890ff',\n    description: '国产优秀大语言模型',\n    models: ['glm-4', 'glm-3-turbo', 'chatglm3-6b'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  siliconflow: {\n    name: '硅基流动',\n    icon: '⚡',\n    color: '#722ed1',\n    description: '高性能AI推理平台',\n    models: ['deepseek-chat', 'qwen-turbo', 'yi-large'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  google: {\n    name: 'Google AI',\n    icon: '🔍',\n    color: '#4285f4',\n    description: 'Google的Gemini模型',\n    models: ['gemini-pro', 'gemini-pro-vision', 'gemini-ultra'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  grok: {\n    name: 'Grok',\n    icon: '🚀',\n    color: '#1da1f2',\n    description: 'xAI的Grok模型',\n    models: ['grok-beta', 'grok-1'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  ollama: {\n    name: 'Ollama',\n    icon: '🏠',\n    color: '#52c41a',\n    description: '本地部署开源模型',\n    models: ['mollysama/rwkv-7-g1:0.4B', 'llama2', 'mistral', 'codellama', 'qwen'],\n    fields: ['base_url', 'model']\n  },\n  custom: {\n    name: '自定义',\n    icon: '⚙️',\n    color: '#8c8c8c',\n    description: 'OpenAI兼容接口',\n    models: [],\n    fields: ['api_key', 'base_url', 'model']\n  }\n};\nconst AIConfigPanel = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [currentProvider, setCurrentProvider] = useState('ollama');\n  const [aiStatus, setAiStatus] = useState({\n    connected: false,\n    status: 'offline'\n  });\n  const [providers, setProviders] = useState([]);\n  const [ollamaModels, setOllamaModels] = useState([]);\n  const [loadingOllamaModels, setLoadingOllamaModels] = useState(false);\n\n  // 获取Ollama模型列表\n  const fetchOllamaModels = async () => {\n    try {\n      setLoadingOllamaModels(true);\n      const response = await axios.get('/api/v1/ai/ollama/models');\n      console.log('Ollama models response:', response.data);\n      const data = response.data;\n      if (data.status === 'error') {\n        var _data$suggestions;\n        // 显示详细的错误信息和建议\n        message.error({\n          content: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Ollama\\u8FDE\\u63A5\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                marginTop: '4px'\n              },\n              children: (_data$suggestions = data.suggestions) === null || _data$suggestions === void 0 ? void 0 : _data$suggestions.slice(0, 2).map((suggestion, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"\\u2022 \", suggestion]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this),\n          duration: 8\n        });\n        setOllamaModels([]);\n        AI_PROVIDERS.ollama.models = [];\n        return [];\n      }\n      const models = data.models.map(model => ({\n        name: model.name,\n        size: model.size,\n        modified_at: model.modified_at\n      }));\n      setOllamaModels(models);\n\n      // 更新AI_PROVIDERS中的ollama模型列表\n      AI_PROVIDERS.ollama.models = models.map(model => model.name);\n      if (models.length === 0) {\n        message.info({\n          content: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"\\u672A\\u68C0\\u6D4B\\u5230\\u672C\\u5730Ollama\\u6A21\\u578B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                marginTop: '4px'\n              },\n              children: \"\\u4F7F\\u7528 ollama pull llama2 \\u4E0B\\u8F7D\\u6A21\\u578B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this),\n          duration: 6\n        });\n      } else {\n        message.success(`检测到 ${models.length} 个本地模型`);\n      }\n      return models;\n    } catch (error) {\n      console.error('获取Ollama模型失败:', error);\n      message.error({\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u65E0\\u6CD5\\u8FDE\\u63A5Ollama\\u670D\\u52A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              marginTop: '4px'\n            },\n            children: \"\\u8BF7\\u786E\\u4FDDOllama\\u5DF2\\u5B89\\u88C5\\u5E76\\u6B63\\u5728\\u8FD0\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this),\n        duration: 8\n      });\n      setOllamaModels([]);\n      AI_PROVIDERS.ollama.models = [];\n      return [];\n    } finally {\n      setLoadingOllamaModels(false);\n    }\n  };\n\n  // 测试Ollama连接\n  const testOllamaConnection = async () => {\n    try {\n      setLoadingOllamaModels(true);\n      const response = await axios.get('/api/v1/ai/ollama/test-connection');\n      const data = response.data;\n      if (data.connected) {\n        message.success({\n          content: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Ollama\\u8FDE\\u63A5\\u6210\\u529F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                marginTop: '4px'\n              },\n              children: [\"\\u68C0\\u6D4B\\u5230 \", data.models_count, \" \\u4E2A\\u6A21\\u578B\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this),\n          duration: 5\n        });\n\n        // 连接成功后自动刷新模型列表\n        await fetchOllamaModels();\n      } else {\n        var _data$suggestions2;\n        message.error({\n          content: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Ollama\\u8FDE\\u63A5\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                marginTop: '4px'\n              },\n              children: (_data$suggestions2 = data.suggestions) === null || _data$suggestions2 === void 0 ? void 0 : _data$suggestions2.slice(0, 2).map((suggestion, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"\\u2022 \", suggestion]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this),\n          duration: 8\n        });\n      }\n    } catch (error) {\n      console.error('测试Ollama连接失败:', error);\n      message.error({\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u8FDE\\u63A5\\u6D4B\\u8BD5\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              marginTop: '4px'\n            },\n            children: \"\\u8BF7\\u68C0\\u67E5Ollama\\u670D\\u52A1\\u662F\\u5426\\u6B63\\u5728\\u8FD0\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this),\n        duration: 8\n      });\n    } finally {\n      setLoadingOllamaModels(false);\n    }\n  };\n\n  // 获取AI信息\n  const fetchAIInfo = async () => {\n    console.log('Fetching AI info...');\n    try {\n      const [providersRes, statusRes] = await Promise.all([axios.get('/api/ai/providers'), axios.get('/api/ai/status')]);\n      console.log('Providers response:', providersRes.data);\n      console.log('Status response:', statusRes.data);\n      setProviders(providersRes.data.providers);\n      setCurrentProvider(providersRes.data.current);\n      setAiStatus(statusRes.data);\n\n      // 如果当前提供商是ollama，获取模型列表\n      if (providersRes.data.current === 'ollama') {\n        await fetchOllamaModels();\n      }\n\n      // 获取当前提供商的配置\n      if (providersRes.data.current) {\n        try {\n          const configRes = await axios.get(`/api/v1/ai/config/${providersRes.data.current}`);\n          console.log('Current provider config:', configRes.data);\n          form.setFieldsValue(configRes.data.config);\n        } catch (error) {\n          console.warn('获取配置失败:', error);\n        }\n      }\n    } catch (error) {\n      console.error('获取AI信息失败:', error);\n      message.error('获取AI信息失败');\n    }\n  };\n\n  // 测试连接\n  const testConnection = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/v1/ai/status');\n      setAiStatus(response.data);\n      if (response.data.connected) {\n        message.success('连接测试成功');\n      } else {\n        message.warning('连接测试失败');\n      }\n    } catch (error) {\n      message.error('连接测试失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 切换提供商\n  const switchProvider = async provider => {\n    console.log('Switching to provider:', provider);\n    try {\n      var _AI_PROVIDERS$provide;\n      setLoading(true);\n      const response = await axios.post('/api/v1/ai/switch-provider', {\n        provider\n      });\n      console.log('Switch provider response:', response.data);\n      setCurrentProvider(provider);\n      message.success(`已切换到 ${((_AI_PROVIDERS$provide = AI_PROVIDERS[provider]) === null || _AI_PROVIDERS$provide === void 0 ? void 0 : _AI_PROVIDERS$provide.name) || provider}`);\n\n      // 如果切换到ollama，先获取模型列表\n      if (provider === 'ollama') {\n        await fetchOllamaModels();\n      }\n\n      // 加载新提供商的配置\n      try {\n        const configRes = await axios.get(`/api/ai/config/${provider}`);\n        console.log('Config loaded:', configRes.data);\n        form.setFieldsValue(configRes.data.config);\n      } catch (error) {\n        console.warn('获取配置失败:', error);\n        // 重置表单为默认值\n        const defaultValues = {\n          max_tokens: 2000,\n          temperature: 0.7,\n          enabled: true\n        };\n\n        // 为Ollama设置默认值\n        if (provider === 'ollama') {\n          defaultValues.model = 'mollysama/rwkv-7-g1:0.4B';\n          defaultValues.base_url = 'http://localhost:11434';\n        }\n        console.log('Setting default values:', defaultValues);\n        form.setFieldsValue(defaultValues);\n      }\n      await fetchAIInfo();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('切换提供商失败:', error);\n      message.error(`切换提供商失败: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 保存配置\n  const saveConfig = async values => {\n    try {\n      setLoading(true);\n      await axios.post('/api/ai/config', {\n        provider: currentProvider,\n        config: values\n      });\n      message.success('配置保存成功');\n      await fetchAIInfo();\n    } catch (error) {\n      console.error('配置保存失败:', error);\n      message.error('配置保存失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchAIInfo();\n  }, []);\n\n  // 提供商选择卡片\n  const ProviderCard = ({\n    provider,\n    isActive,\n    onClick,\n    disabled = false\n  }) => {\n    const config = AI_PROVIDERS[provider];\n    const handleClick = () => {\n      if (disabled) return;\n      console.log('Provider card clicked:', provider);\n      onClick(provider);\n    };\n    return /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      hoverable: !disabled,\n      className: `provider-card ${isActive ? 'active' : ''} ${disabled ? 'disabled' : ''}`,\n      onClick: handleClick,\n      style: {\n        borderColor: isActive ? config.color : '#d9d9d9',\n        backgroundColor: isActive ? `${config.color}10` : disabled ? '#f5f5f5' : '#fff',\n        cursor: disabled ? 'not-allowed' : 'pointer',\n        transition: 'all 0.3s',\n        minHeight: '120px',\n        border: isActive ? `2px solid ${config.color}` : '1px solid #d9d9d9',\n        opacity: disabled ? 0.6 : 1\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '24px',\n            marginBottom: '8px'\n          },\n          children: config.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            color: disabled ? '#999' : config.color,\n            marginBottom: '4px'\n          },\n          children: config.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: disabled ? '#999' : '#666',\n            lineHeight: '1.4'\n          },\n          children: config.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), isActive && !disabled && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: config.color,\n            size: \"small\",\n            children: \"\\u5F53\\u524D\\u9009\\u62E9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), disabled && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"default\",\n            size: \"small\",\n            children: \"\\u52A0\\u8F7D\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 状态指示器\n  const StatusIndicator = () => {\n    var _AI_PROVIDERS$current, _AI_PROVIDERS$current2, _AI_PROVIDERS$current3;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tag, {\n          color: aiStatus.connected ? 'green' : 'red',\n          icon: aiStatus.connected ? /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 38\n          }, this) : /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 64\n          }, this),\n          style: {\n            fontSize: '14px',\n            padding: '4px 12px'\n          },\n          children: aiStatus.connected ? '在线' : '离线'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u5F53\\u524D\\u63D0\\u4F9B\\u5546\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            color: (_AI_PROVIDERS$current = AI_PROVIDERS[currentProvider]) === null || _AI_PROVIDERS$current === void 0 ? void 0 : _AI_PROVIDERS$current.color\n          },\n          children: [(_AI_PROVIDERS$current2 = AI_PROVIDERS[currentProvider]) === null || _AI_PROVIDERS$current2 === void 0 ? void 0 : _AI_PROVIDERS$current2.icon, \" \", (_AI_PROVIDERS$current3 = AI_PROVIDERS[currentProvider]) === null || _AI_PROVIDERS$current3 === void 0 ? void 0 : _AI_PROVIDERS$current3.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 15\n        }, this),\n        onClick: testConnection,\n        loading: loading,\n        size: \"small\",\n        children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 5\n    }, this);\n  };\n  const currentConfig = AI_PROVIDERS[currentProvider];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 24,\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 18,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(RobotOutlined, {\n                style: {\n                  fontSize: '32px',\n                  color: currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.color\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0\n                },\n                children: \"AI\\u914D\\u7F6E\\u7BA1\\u7406\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u914D\\u7F6E\\u548C\\u7BA1\\u7406\\u591A\\u79CDAI\\u63D0\\u4F9B\\u5546\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [\"\\u9009\\u62E9AI\\u63D0\\u4F9B\\u5546\", /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: [Object.keys(AI_PROVIDERS).length, \" \\u4E2A\\u53EF\\u7528\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this),\n          style: {\n            marginBottom: '24px'\n          },\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            onClick: fetchAIInfo,\n            loading: loading,\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 23\n            }, this),\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this),\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '40px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"\\u6B63\\u5728\\u52A0\\u8F7DAI\\u63D0\\u4F9B\\u5546...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [16, 16],\n            children: Object.keys(AI_PROVIDERS).map(provider => /*#__PURE__*/_jsxDEV(Col, {\n              span: 6,\n              children: /*#__PURE__*/_jsxDEV(ProviderCard, {\n                provider: provider,\n                isActive: currentProvider === provider,\n                onClick: switchProvider,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 21\n              }, this)\n            }, provider, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: `${currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.name} 配置`,\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            onFinish: saveConfig,\n            initialValues: {\n              max_tokens: 2000,\n              temperature: 0.7,\n              enabled: true,\n              model: currentProvider === 'ollama' ? 'mollysama/rwkv-7-g1:0.4B' : undefined,\n              base_url: currentProvider === 'ollama' ? 'http://localhost:11434' : undefined\n            },\n            children: [(currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.fields.includes('api_key')) && /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"api_key\",\n              label: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"API Key\", /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u4ECE\\u5BF9\\u5E94\\u5E73\\u53F0\\u83B7\\u53D6\\u7684API\\u5BC6\\u94A5\",\n                  children: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                placeholder: `请输入${currentConfig.name} API Key`,\n                iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 57\n                }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 74\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this), (currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.fields.includes('base_url')) && /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"base_url\",\n              label: \"API\\u5730\\u5740\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"API\\u670D\\u52A1\\u5730\\u5740\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this), (currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.fields.includes('model')) && /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"model\",\n              label: /*#__PURE__*/_jsxDEV(Space, {\n                children: [\"\\u6A21\\u578B\", currentProvider === 'ollama' && /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 35\n                    }, this),\n                    onClick: fetchOllamaModels,\n                    loading: loadingOllamaModels,\n                    style: {\n                      padding: 0\n                    },\n                    children: \"\\u5237\\u65B0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    size: \"small\",\n                    onClick: testOllamaConnection,\n                    style: {\n                      padding: 0\n                    },\n                    children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: currentProvider === 'ollama' ? \"选择本地模型\" : \"选择模型\",\n                loading: currentProvider === 'ollama' && loadingOllamaModels,\n                notFoundContent: currentProvider === 'ollama' ? loadingOllamaModels ? \"加载中...\" : \"未找到本地模型，请先下载模型\" : \"未找到模型\",\n                children: currentProvider === 'ollama' ? ollamaModels.map(model => /*#__PURE__*/_jsxDEV(Option, {\n                  value: model.name,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: model.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      style: {\n                        fontSize: '12px'\n                      },\n                      children: [(model.size / (1024 * 1024 * 1024)).toFixed(1), \"GB\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 27\n                  }, this)\n                }, model.name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 25\n                }, this)) : currentConfig.models.map(model => /*#__PURE__*/_jsxDEV(Option, {\n                  value: model,\n                  children: model\n                }, model, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"max_tokens\",\n                  label: \"\\u6700\\u5927Token\\u6570\",\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 100,\n                    max: 8000,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"2000\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"temperature\",\n                  label: \"\\u6E29\\u5EA6\\u53C2\\u6570\",\n                  children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                    min: 0,\n                    max: 2,\n                    step: 0.1,\n                    style: {\n                      width: '100%'\n                    },\n                    placeholder: \"0.7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"enabled\",\n                  label: \"\\u542F\\u7528\",\n                  valuePropName: \"checked\",\n                  children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 25\n                }, this),\n                loading: loading,\n                size: \"large\",\n                children: \"\\u4FDD\\u5B58\\u914D\\u7F6E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u4F7F\\u7528\\u8BF4\\u660E\",\n          size: \"small\",\n          style: {\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              lineHeight: '1.8'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.icon, \" \", currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 18\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this), currentProvider === 'openai' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u2022 \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://platform.openai.com/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"OpenAI\\u5B98\\u7F51\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 27\n                }, this), \" \\u83B7\\u53D6API Key\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u652F\\u6301GPT-3.5\\u548CGPT-4\\u7CFB\\u5217\\u6A21\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 17\n            }, this), currentProvider === 'claude' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u2022 \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://console.anthropic.com/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"Anthropic\\u5B98\\u7F51\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 27\n                }, this), \" \\u83B7\\u53D6API Key\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u652F\\u6301Claude 3\\u7CFB\\u5217\\u6A21\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 17\n            }, this), currentProvider === 'zhipu' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u2022 \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://open.bigmodel.cn/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"\\u667A\\u8C31AI\\u5B98\\u7F51\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 27\n                }, this), \" \\u83B7\\u53D6API Key\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u56FD\\u4EA7\\u4F18\\u79C0\\u5927\\u8BED\\u8A00\\u6A21\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 17\n            }, this), currentProvider === 'siliconflow' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u2022 \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://siliconflow.cn/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"\\u7845\\u57FA\\u6D41\\u52A8\\u5B98\\u7F51\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 27\n                }, this), \" \\u83B7\\u53D6API Key\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u9AD8\\u6027\\u80FDAI\\u63A8\\u7406\\u5E73\\u53F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u652F\\u6301\\u591A\\u79CD\\u5F00\\u6E90\\u6A21\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 17\n            }, this), currentProvider === 'google' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u2022 \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://ai.google.dev/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"Google AI Studio\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 27\n                }, this), \" \\u83B7\\u53D6API Key\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u652F\\u6301Gemini\\u7CFB\\u5217\\u6A21\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u591A\\u6A21\\u6001\\u80FD\\u529B\\u5F3A\\u5927\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 17\n            }, this), currentProvider === 'grok' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u2022 \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://x.ai/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"xAI\\u5B98\\u7F51\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 27\n                }, this), \" \\u83B7\\u53D6API Key\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u652F\\u6301Grok\\u7CFB\\u5217\\u6A21\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u5B9E\\u65F6\\u4FE1\\u606F\\u83B7\\u53D6\\u80FD\\u529B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this), currentProvider === 'ollama' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u9700\\u8981\\u672C\\u5730\\u5B89\\u88C5Ollama\\u670D\\u52A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u652F\\u6301\\u591A\\u79CD\\u5F00\\u6E90\\u6A21\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u6570\\u636E\\u5B8C\\u5168\\u672C\\u5730\\u5316\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u2022 \\u4E0B\\u8F7D\\u5730\\u5740: \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://ollama.ai/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"ollama.ai\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                style: {\n                  margin: '8px 0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\u672C\\u5730\\u6A21\\u578B\\u72B6\\u6001:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 742,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 19\n              }, this), loadingOllamaModels ? /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u6B63\\u5728\\u68C0\\u6D4B\\u6A21\\u578B...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 21\n              }, this) : ollamaModels.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u2022 \\u5DF2\\u5B89\\u88C5 \", ollamaModels.length, \" \\u4E2A\\u6A21\\u578B\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 23\n                }, this), ollamaModels.slice(0, 3).map(model => /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    fontSize: '11px',\n                    margin: '2px 0'\n                  },\n                  children: [\"- \", model.name, \" (\", (model.size / (1024 * 1024 * 1024)).toFixed(1), \"GB)\"]\n                }, model.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 25\n                }, this)), ollamaModels.length > 3 && /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    fontSize: '11px',\n                    margin: '2px 0'\n                  },\n                  children: [\"... \\u8FD8\\u6709 \", ollamaModels.length - 3, \" \\u4E2A\\u6A21\\u578B\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u2022 \\u672A\\u68C0\\u6D4B\\u5230\\u672C\\u5730\\u6A21\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    fontSize: '11px'\n                  },\n                  children: \"\\u4F7F\\u7528 ollama pull <model> \\u4E0B\\u8F7D\\u6A21\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 17\n            }, this), currentProvider === 'custom' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u652F\\u6301OpenAI\\u517C\\u5BB9\\u7684API\\u63A5\\u53E3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u53EF\\u914D\\u7F6E\\u81EA\\u5B9A\\u4E49\\u670D\\u52A1\\u5730\\u5740\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u2022 \\u9002\\u7528\\u4E8E\\u79C1\\u6709\\u90E8\\u7F72\\u7684\\u6A21\\u578B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this), aiStatus.error && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u8FDE\\u63A5\\u9519\\u8BEF\",\n          description: aiStatus.error,\n          type: \"error\",\n          size: \"small\",\n          style: {\n            marginBottom: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5FEB\\u901F\\u64CD\\u4F5C\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              block: true,\n              onClick: fetchAIInfo,\n              loading: loading,\n              children: \"\\u5237\\u65B0\\u72B6\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              block: true,\n              onClick: testConnection,\n              loading: loading,\n              children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this), currentProvider === 'ollama' && /*#__PURE__*/_jsxDEV(Button, {\n              block: true,\n              onClick: fetchOllamaModels,\n              loading: loadingOllamaModels,\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 25\n              }, this),\n              children: \"\\u5237\\u65B0\\u6A21\\u578B\\u5217\\u8868\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 466,\n    columnNumber: 5\n  }, this);\n};\n_s(AIConfigPanel, \"dzotDd7KsfFI3VWMLGn18hoCsmY=\", false, function () {\n  return [Form.useForm];\n});\n_c = AIConfigPanel;\nexport default AIConfigPanel;\nvar _c;\n$RefreshReg$(_c, \"AIConfigPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Form", "Input", "Select", "<PERSON><PERSON>", "Space", "Tag", "<PERSON><PERSON>", "Row", "Col", "Divider", "Typography", "Switch", "InputNumber", "message", "<PERSON><PERSON><PERSON>", "RobotOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "ReloadOutlined", "SaveOutlined", "EyeInvisibleOutlined", "EyeTwoTone", "InfoCircleOutlined", "axios", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "AI_PROVIDERS", "openai", "name", "icon", "color", "description", "models", "fields", "claude", "<PERSON><PERSON><PERSON>", "siliconflow", "google", "grok", "ollama", "custom", "AIConfigPanel", "_s", "form", "useForm", "loading", "setLoading", "currentProvider", "setCurrentProvider", "aiStatus", "setAiStatus", "connected", "status", "providers", "setProviders", "ollamaModels", "setOllamaModels", "loadingOllamaModels", "setLoadingOllamaModels", "fetchOllamaModels", "response", "get", "console", "log", "data", "_data$suggestions", "error", "content", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "marginTop", "suggestions", "slice", "map", "suggestion", "index", "duration", "model", "size", "modified_at", "length", "info", "success", "testOllamaConnection", "models_count", "_data$suggestions2", "fetchAIInfo", "providersRes", "statusRes", "Promise", "all", "current", "configRes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "warn", "testConnection", "warning", "switchProvider", "provider", "_AI_PROVIDERS$provide", "post", "defaultValues", "max_tokens", "temperature", "enabled", "base_url", "_error$response", "_error$response$data", "detail", "saveConfig", "values", "ProviderCard", "isActive", "onClick", "disabled", "handleClick", "hoverable", "className", "borderColor", "backgroundColor", "cursor", "transition", "minHeight", "border", "opacity", "textAlign", "padding", "marginBottom", "fontWeight", "lineHeight", "StatusIndicator", "_AI_PROVIDERS$current", "_AI_PROVIDERS$current2", "_AI_PROVIDERS$current3", "type", "strong", "currentConfig", "max<PERSON><PERSON><PERSON>", "margin", "gutter", "align", "span", "level", "title", "Object", "keys", "extra", "direction", "layout", "onFinish", "initialValues", "undefined", "includes", "<PERSON><PERSON>", "label", "Password", "placeholder", "iconRender", "visible", "notFoundContent", "value", "display", "justifyContent", "alignItems", "toFixed", "min", "max", "width", "step", "valuePropName", "htmlType", "href", "target", "rel", "block", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/components/AIConfigPanel.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Form,\n  Input,\n  Select,\n  Button,\n  Space,\n  Tag,\n  Alert,\n  Row,\n  Col,\n  Divider,\n  Typography,\n  Switch,\n  InputNumber,\n  message,\n  Tooltip\n} from 'antd';\nimport {\n  RobotOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ReloadOutlined,\n  SaveOutlined,\n  EyeInvisibleOutlined,\n  EyeTwoTone,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport axios from 'axios';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\n// AI提供商配置信息\nconst AI_PROVIDERS = {\n  openai: {\n    name: 'OpenAI',\n    icon: '🤖',\n    color: '#10a37f',\n    description: '最先进的GPT模型',\n    models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  claude: {\n    name: '<PERSON>',\n    icon: '🧠',\n    color: '#ff6b35',\n    description: 'Anthropic的Claude模型',\n    models: ['claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  zhipu: {\n    name: '智谱AI',\n    icon: '🇨🇳',\n    color: '#1890ff',\n    description: '国产优秀大语言模型',\n    models: ['glm-4', 'glm-3-turbo', 'chatglm3-6b'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  siliconflow: {\n    name: '硅基流动',\n    icon: '⚡',\n    color: '#722ed1',\n    description: '高性能AI推理平台',\n    models: ['deepseek-chat', 'qwen-turbo', 'yi-large'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  google: {\n    name: 'Google AI',\n    icon: '🔍',\n    color: '#4285f4',\n    description: 'Google的Gemini模型',\n    models: ['gemini-pro', 'gemini-pro-vision', 'gemini-ultra'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  grok: {\n    name: 'Grok',\n    icon: '🚀',\n    color: '#1da1f2',\n    description: 'xAI的Grok模型',\n    models: ['grok-beta', 'grok-1'],\n    fields: ['api_key', 'base_url', 'model']\n  },\n  ollama: {\n    name: 'Ollama',\n    icon: '🏠',\n    color: '#52c41a',\n    description: '本地部署开源模型',\n    models: ['mollysama/rwkv-7-g1:0.4B', 'llama2', 'mistral', 'codellama', 'qwen'],\n    fields: ['base_url', 'model']\n  },\n  custom: {\n    name: '自定义',\n    icon: '⚙️',\n    color: '#8c8c8c',\n    description: 'OpenAI兼容接口',\n    models: [],\n    fields: ['api_key', 'base_url', 'model']\n  }\n};\n\nconst AIConfigPanel = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [currentProvider, setCurrentProvider] = useState('ollama');\n  const [aiStatus, setAiStatus] = useState({ connected: false, status: 'offline' });\n  const [providers, setProviders] = useState([]);\n  const [ollamaModels, setOllamaModels] = useState([]);\n  const [loadingOllamaModels, setLoadingOllamaModels] = useState(false);\n\n  // 获取Ollama模型列表\n  const fetchOllamaModels = async () => {\n    try {\n      setLoadingOllamaModels(true);\n      const response = await axios.get('/api/v1/ai/ollama/models');\n      console.log('Ollama models response:', response.data);\n\n      const data = response.data;\n\n      if (data.status === 'error') {\n        // 显示详细的错误信息和建议\n        message.error({\n          content: (\n            <div>\n              <div>Ollama连接失败</div>\n              <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                {data.suggestions?.slice(0, 2).map((suggestion, index) => (\n                  <div key={index}>• {suggestion}</div>\n                ))}\n              </div>\n            </div>\n          ),\n          duration: 8\n        });\n        setOllamaModels([]);\n        AI_PROVIDERS.ollama.models = [];\n        return [];\n      }\n\n      const models = data.models.map(model => ({\n        name: model.name,\n        size: model.size,\n        modified_at: model.modified_at\n      }));\n\n      setOllamaModels(models);\n\n      // 更新AI_PROVIDERS中的ollama模型列表\n      AI_PROVIDERS.ollama.models = models.map(model => model.name);\n\n      if (models.length === 0) {\n        message.info({\n          content: (\n            <div>\n              <div>未检测到本地Ollama模型</div>\n              <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                使用 ollama pull llama2 下载模型\n              </div>\n            </div>\n          ),\n          duration: 6\n        });\n      } else {\n        message.success(`检测到 ${models.length} 个本地模型`);\n      }\n\n      return models;\n    } catch (error) {\n      console.error('获取Ollama模型失败:', error);\n      message.error({\n        content: (\n          <div>\n            <div>无法连接Ollama服务</div>\n            <div style={{ fontSize: '12px', marginTop: '4px' }}>\n              请确保Ollama已安装并正在运行\n            </div>\n          </div>\n        ),\n        duration: 8\n      });\n      setOllamaModels([]);\n      AI_PROVIDERS.ollama.models = [];\n      return [];\n    } finally {\n      setLoadingOllamaModels(false);\n    }\n  };\n\n  // 测试Ollama连接\n  const testOllamaConnection = async () => {\n    try {\n      setLoadingOllamaModels(true);\n      const response = await axios.get('/api/v1/ai/ollama/test-connection');\n      const data = response.data;\n\n      if (data.connected) {\n        message.success({\n          content: (\n            <div>\n              <div>Ollama连接成功</div>\n              <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                检测到 {data.models_count} 个模型\n              </div>\n            </div>\n          ),\n          duration: 5\n        });\n\n        // 连接成功后自动刷新模型列表\n        await fetchOllamaModels();\n      } else {\n        message.error({\n          content: (\n            <div>\n              <div>Ollama连接失败</div>\n              <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                {data.suggestions?.slice(0, 2).map((suggestion, index) => (\n                  <div key={index}>• {suggestion}</div>\n                ))}\n              </div>\n            </div>\n          ),\n          duration: 8\n        });\n      }\n    } catch (error) {\n      console.error('测试Ollama连接失败:', error);\n      message.error({\n        content: (\n          <div>\n            <div>连接测试失败</div>\n            <div style={{ fontSize: '12px', marginTop: '4px' }}>\n              请检查Ollama服务是否正在运行\n            </div>\n          </div>\n        ),\n        duration: 8\n      });\n    } finally {\n      setLoadingOllamaModels(false);\n    }\n  };\n\n  // 获取AI信息\n  const fetchAIInfo = async () => {\n    console.log('Fetching AI info...');\n    try {\n      const [providersRes, statusRes] = await Promise.all([\n        axios.get('/api/ai/providers'),\n        axios.get('/api/ai/status')\n      ]);\n\n      console.log('Providers response:', providersRes.data);\n      console.log('Status response:', statusRes.data);\n\n      setProviders(providersRes.data.providers);\n      setCurrentProvider(providersRes.data.current);\n      setAiStatus(statusRes.data);\n\n      // 如果当前提供商是ollama，获取模型列表\n      if (providersRes.data.current === 'ollama') {\n        await fetchOllamaModels();\n      }\n\n      // 获取当前提供商的配置\n      if (providersRes.data.current) {\n        try {\n          const configRes = await axios.get(`/api/v1/ai/config/${providersRes.data.current}`);\n          console.log('Current provider config:', configRes.data);\n          form.setFieldsValue(configRes.data.config);\n        } catch (error) {\n          console.warn('获取配置失败:', error);\n        }\n      }\n    } catch (error) {\n      console.error('获取AI信息失败:', error);\n      message.error('获取AI信息失败');\n    }\n  };\n\n  // 测试连接\n  const testConnection = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/v1/ai/status');\n      setAiStatus(response.data);\n\n      if (response.data.connected) {\n        message.success('连接测试成功');\n      } else {\n        message.warning('连接测试失败');\n      }\n    } catch (error) {\n      message.error('连接测试失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 切换提供商\n  const switchProvider = async (provider) => {\n    console.log('Switching to provider:', provider);\n    try {\n      setLoading(true);\n\n      const response = await axios.post('/api/v1/ai/switch-provider', { provider });\n      console.log('Switch provider response:', response.data);\n\n      setCurrentProvider(provider);\n      message.success(`已切换到 ${AI_PROVIDERS[provider]?.name || provider}`);\n\n      // 如果切换到ollama，先获取模型列表\n      if (provider === 'ollama') {\n        await fetchOllamaModels();\n      }\n\n      // 加载新提供商的配置\n      try {\n        const configRes = await axios.get(`/api/ai/config/${provider}`);\n        console.log('Config loaded:', configRes.data);\n        form.setFieldsValue(configRes.data.config);\n      } catch (error) {\n        console.warn('获取配置失败:', error);\n        // 重置表单为默认值\n        const defaultValues = {\n          max_tokens: 2000,\n          temperature: 0.7,\n          enabled: true\n        };\n\n        // 为Ollama设置默认值\n        if (provider === 'ollama') {\n          defaultValues.model = 'mollysama/rwkv-7-g1:0.4B';\n          defaultValues.base_url = 'http://localhost:11434';\n        }\n\n        console.log('Setting default values:', defaultValues);\n        form.setFieldsValue(defaultValues);\n      }\n\n      await fetchAIInfo();\n    } catch (error) {\n      console.error('切换提供商失败:', error);\n      message.error(`切换提供商失败: ${error.response?.data?.detail || error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 保存配置\n  const saveConfig = async (values) => {\n    try {\n      setLoading(true);\n      await axios.post('/api/ai/config', {\n        provider: currentProvider,\n        config: values\n      });\n      message.success('配置保存成功');\n      await fetchAIInfo();\n    } catch (error) {\n      console.error('配置保存失败:', error);\n      message.error('配置保存失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchAIInfo();\n  }, []);\n\n  // 提供商选择卡片\n  const ProviderCard = ({ provider, isActive, onClick, disabled = false }) => {\n    const config = AI_PROVIDERS[provider];\n\n    const handleClick = () => {\n      if (disabled) return;\n      console.log('Provider card clicked:', provider);\n      onClick(provider);\n    };\n\n    return (\n      <Card\n        size=\"small\"\n        hoverable={!disabled}\n        className={`provider-card ${isActive ? 'active' : ''} ${disabled ? 'disabled' : ''}`}\n        onClick={handleClick}\n        style={{\n          borderColor: isActive ? config.color : '#d9d9d9',\n          backgroundColor: isActive ? `${config.color}10` : disabled ? '#f5f5f5' : '#fff',\n          cursor: disabled ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s',\n          minHeight: '120px',\n          border: isActive ? `2px solid ${config.color}` : '1px solid #d9d9d9',\n          opacity: disabled ? 0.6 : 1\n        }}\n      >\n        <div style={{ textAlign: 'center', padding: '8px' }}>\n          <div style={{ fontSize: '24px', marginBottom: '8px' }}>\n            {config.icon}\n          </div>\n          <div style={{\n            fontWeight: 'bold',\n            color: disabled ? '#999' : config.color,\n            marginBottom: '4px'\n          }}>\n            {config.name}\n          </div>\n          <div style={{\n            fontSize: '12px',\n            color: disabled ? '#999' : '#666',\n            lineHeight: '1.4'\n          }}>\n            {config.description}\n          </div>\n          {isActive && !disabled && (\n            <div style={{ marginTop: '8px' }}>\n              <Tag color={config.color} size=\"small\">当前选择</Tag>\n            </div>\n          )}\n          {disabled && (\n            <div style={{ marginTop: '8px' }}>\n              <Tag color=\"default\" size=\"small\">加载中...</Tag>\n            </div>\n          )}\n        </div>\n      </Card>\n    );\n  };\n\n  // 状态指示器\n  const StatusIndicator = () => (\n    <div style={{ textAlign: 'center', padding: '16px' }}>\n      <div style={{ marginBottom: '12px' }}>\n        <Tag\n          color={aiStatus.connected ? 'green' : 'red'}\n          icon={aiStatus.connected ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}\n          style={{ fontSize: '14px', padding: '4px 12px' }}\n        >\n          {aiStatus.connected ? '在线' : '离线'}\n        </Tag>\n      </div>\n      <div style={{ marginBottom: '12px' }}>\n        <Text type=\"secondary\">当前提供商</Text>\n        <br />\n        <Text strong style={{ color: AI_PROVIDERS[currentProvider]?.color }}>\n          {AI_PROVIDERS[currentProvider]?.icon} {AI_PROVIDERS[currentProvider]?.name}\n        </Text>\n      </div>\n      <Button\n        type=\"primary\"\n        icon={<ReloadOutlined />}\n        onClick={testConnection}\n        loading={loading}\n        size=\"small\"\n      >\n        测试连接\n      </Button>\n    </div>\n  );\n\n  const currentConfig = AI_PROVIDERS[currentProvider];\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* 状态概览 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Row gutter={24} align=\"middle\">\n          <Col span={18}>\n            <Space size=\"large\">\n              <div>\n                <RobotOutlined style={{ fontSize: '32px', color: currentConfig?.color }} />\n              </div>\n              <div>\n                <Title level={4} style={{ margin: 0 }}>AI配置管理</Title>\n                <Text type=\"secondary\">配置和管理多种AI提供商</Text>\n              </div>\n            </Space>\n          </Col>\n          <Col span={6}>\n            <StatusIndicator />\n          </Col>\n        </Row>\n      </Card>\n\n      <Row gutter={24}>\n        {/* 提供商选择 */}\n        <Col span={16}>\n          <Card\n            title={\n              <Space>\n                选择AI提供商\n                <Tag color=\"blue\">{Object.keys(AI_PROVIDERS).length} 个可用</Tag>\n              </Space>\n            }\n            style={{ marginBottom: '24px' }}\n            extra={\n              <Button\n                size=\"small\"\n                onClick={fetchAIInfo}\n                loading={loading}\n                icon={<ReloadOutlined />}\n              >\n                刷新\n              </Button>\n            }\n          >\n            {loading ? (\n              <div style={{ textAlign: 'center', padding: '40px' }}>\n                <Space direction=\"vertical\">\n                  <div>正在加载AI提供商...</div>\n                </Space>\n              </div>\n            ) : (\n              <Row gutter={[16, 16]}>\n                {Object.keys(AI_PROVIDERS).map(provider => (\n                  <Col span={6} key={provider}>\n                    <ProviderCard\n                      provider={provider}\n                      isActive={currentProvider === provider}\n                      onClick={switchProvider}\n                      disabled={loading}\n                    />\n                  </Col>\n                ))}\n              </Row>\n            )}\n          </Card>\n\n          {/* 配置表单 */}\n          <Card title={`${currentConfig?.name} 配置`}>\n            <Form\n              form={form}\n              layout=\"vertical\"\n              onFinish={saveConfig}\n              initialValues={{\n                max_tokens: 2000,\n                temperature: 0.7,\n                enabled: true,\n                model: currentProvider === 'ollama' ? 'mollysama/rwkv-7-g1:0.4B' : undefined,\n                base_url: currentProvider === 'ollama' ? 'http://localhost:11434' : undefined\n              }}\n            >\n              {/* API Key */}\n              {currentConfig?.fields.includes('api_key') && (\n                <Form.Item\n                  name=\"api_key\"\n                  label={\n                    <Space>\n                      API Key\n                      <Tooltip title=\"从对应平台获取的API密钥\">\n                        <InfoCircleOutlined />\n                      </Tooltip>\n                    </Space>\n                  }\n                >\n                  <Input.Password\n                    placeholder={`请输入${currentConfig.name} API Key`}\n                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                  />\n                </Form.Item>\n              )}\n\n              {/* Base URL */}\n              {currentConfig?.fields.includes('base_url') && (\n                <Form.Item\n                  name=\"base_url\"\n                  label=\"API地址\"\n                >\n                  <Input placeholder=\"API服务地址\" />\n                </Form.Item>\n              )}\n\n              {/* 模型选择 */}\n              {currentConfig?.fields.includes('model') && (\n                <Form.Item\n                  name=\"model\"\n                  label={\n                    <Space>\n                      模型\n                      {currentProvider === 'ollama' && (\n                        <Space>\n                          <Button\n                            type=\"link\"\n                            size=\"small\"\n                            icon={<ReloadOutlined />}\n                            onClick={fetchOllamaModels}\n                            loading={loadingOllamaModels}\n                            style={{ padding: 0 }}\n                          >\n                            刷新\n                          </Button>\n                          <Button\n                            type=\"link\"\n                            size=\"small\"\n                            onClick={testOllamaConnection}\n                            style={{ padding: 0 }}\n                          >\n                            测试连接\n                          </Button>\n                        </Space>\n                      )}\n                    </Space>\n                  }\n                >\n                  <Select\n                    placeholder={currentProvider === 'ollama' ? \"选择本地模型\" : \"选择模型\"}\n                    loading={currentProvider === 'ollama' && loadingOllamaModels}\n                    notFoundContent={\n                      currentProvider === 'ollama'\n                        ? (loadingOllamaModels ? \"加载中...\" : \"未找到本地模型，请先下载模型\")\n                        : \"未找到模型\"\n                    }\n                  >\n                    {currentProvider === 'ollama' ? (\n                      ollamaModels.map(model => (\n                        <Option key={model.name} value={model.name}>\n                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                            <span>{model.name}</span>\n                            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                              {(model.size / (1024 * 1024 * 1024)).toFixed(1)}GB\n                            </Text>\n                          </div>\n                        </Option>\n                      ))\n                    ) : (\n                      currentConfig.models.map(model => (\n                        <Option key={model} value={model}>{model}</Option>\n                      ))\n                    )}\n                  </Select>\n                </Form.Item>\n              )}\n\n              <Divider />\n\n              {/* 通用参数 */}\n              <Row gutter={16}>\n                <Col span={8}>\n                  <Form.Item name=\"max_tokens\" label=\"最大Token数\">\n                    <InputNumber\n                      min={100}\n                      max={8000}\n                      style={{ width: '100%' }}\n                      placeholder=\"2000\"\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item name=\"temperature\" label=\"温度参数\">\n                    <InputNumber\n                      min={0}\n                      max={2}\n                      step={0.1}\n                      style={{ width: '100%' }}\n                      placeholder=\"0.7\"\n                    />\n                  </Form.Item>\n                </Col>\n                <Col span={8}>\n                  <Form.Item name=\"enabled\" label=\"启用\" valuePropName=\"checked\">\n                    <Switch />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  icon={<SaveOutlined />}\n                  loading={loading}\n                  size=\"large\"\n                >\n                  保存配置\n                </Button>\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        {/* 侧边栏信息 */}\n        <Col span={8}>\n          <Card title=\"使用说明\" size=\"small\" style={{ marginBottom: '16px' }}>\n            <div style={{ fontSize: '12px', lineHeight: '1.8' }}>\n              <p><strong>{currentConfig?.icon} {currentConfig?.name}</strong></p>\n              <p>{currentConfig?.description}</p>\n\n              {currentProvider === 'openai' && (\n                <div>\n                  <p>• 访问 <a href=\"https://platform.openai.com/\" target=\"_blank\" rel=\"noopener noreferrer\">OpenAI官网</a> 获取API Key</p>\n                  <p>• 支持GPT-3.5和GPT-4系列模型</p>\n                </div>\n              )}\n\n              {currentProvider === 'claude' && (\n                <div>\n                  <p>• 访问 <a href=\"https://console.anthropic.com/\" target=\"_blank\" rel=\"noopener noreferrer\">Anthropic官网</a> 获取API Key</p>\n                  <p>• 支持Claude 3系列模型</p>\n                </div>\n              )}\n\n              {currentProvider === 'zhipu' && (\n                <div>\n                  <p>• 访问 <a href=\"https://open.bigmodel.cn/\" target=\"_blank\" rel=\"noopener noreferrer\">智谱AI官网</a> 获取API Key</p>\n                  <p>• 国产优秀大语言模型</p>\n                </div>\n              )}\n\n              {currentProvider === 'siliconflow' && (\n                <div>\n                  <p>• 访问 <a href=\"https://siliconflow.cn/\" target=\"_blank\" rel=\"noopener noreferrer\">硅基流动官网</a> 获取API Key</p>\n                  <p>• 高性能AI推理平台</p>\n                  <p>• 支持多种开源模型</p>\n                </div>\n              )}\n\n              {currentProvider === 'google' && (\n                <div>\n                  <p>• 访问 <a href=\"https://ai.google.dev/\" target=\"_blank\" rel=\"noopener noreferrer\">Google AI Studio</a> 获取API Key</p>\n                  <p>• 支持Gemini系列模型</p>\n                  <p>• 多模态能力强大</p>\n                </div>\n              )}\n\n              {currentProvider === 'grok' && (\n                <div>\n                  <p>• 访问 <a href=\"https://x.ai/\" target=\"_blank\" rel=\"noopener noreferrer\">xAI官网</a> 获取API Key</p>\n                  <p>• 支持Grok系列模型</p>\n                  <p>• 实时信息获取能力</p>\n                </div>\n              )}\n\n              {currentProvider === 'ollama' && (\n                <div>\n                  <p>• 需要本地安装Ollama服务</p>\n                  <p>• 支持多种开源模型</p>\n                  <p>• 数据完全本地化</p>\n                  <p>• 下载地址: <a href=\"https://ollama.ai/\" target=\"_blank\" rel=\"noopener noreferrer\">ollama.ai</a></p>\n                  <Divider style={{ margin: '8px 0' }} />\n                  <p><strong>本地模型状态:</strong></p>\n                  {loadingOllamaModels ? (\n                    <p>• 正在检测模型...</p>\n                  ) : ollamaModels.length > 0 ? (\n                    <div>\n                      <p>• 已安装 {ollamaModels.length} 个模型</p>\n                      {ollamaModels.slice(0, 3).map(model => (\n                        <p key={model.name} style={{ fontSize: '11px', margin: '2px 0' }}>\n                          - {model.name} ({(model.size / (1024 * 1024 * 1024)).toFixed(1)}GB)\n                        </p>\n                      ))}\n                      {ollamaModels.length > 3 && (\n                        <p style={{ fontSize: '11px', margin: '2px 0' }}>\n                          ... 还有 {ollamaModels.length - 3} 个模型\n                        </p>\n                      )}\n                    </div>\n                  ) : (\n                    <div>\n                      <p>• 未检测到本地模型</p>\n                      <p style={{ fontSize: '11px' }}>使用 ollama pull &lt;model&gt; 下载模型</p>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {currentProvider === 'custom' && (\n                <div>\n                  <p>• 支持OpenAI兼容的API接口</p>\n                  <p>• 可配置自定义服务地址</p>\n                  <p>• 适用于私有部署的模型</p>\n                </div>\n              )}\n            </div>\n          </Card>\n\n          {aiStatus.error && (\n            <Alert\n              message=\"连接错误\"\n              description={aiStatus.error}\n              type=\"error\"\n              size=\"small\"\n              style={{ marginBottom: '16px' }}\n            />\n          )}\n\n          <Card title=\"快速操作\" size=\"small\">\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <Button block onClick={fetchAIInfo} loading={loading}>\n                刷新状态\n              </Button>\n              <Button block onClick={testConnection} loading={loading}>\n                测试连接\n              </Button>\n              {currentProvider === 'ollama' && (\n                <Button\n                  block\n                  onClick={fetchOllamaModels}\n                  loading={loadingOllamaModels}\n                  icon={<ReloadOutlined />}\n                >\n                  刷新模型列表\n                </Button>\n              )}\n            </Space>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default AIConfigPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,OAAO,EACPC,OAAO,QACF,MAAM;AACb,SACEC,aAAa,EACbC,mBAAmB,EACnBC,yBAAyB,EACzBC,cAAc,EACdC,YAAY,EACZC,oBAAoB,EACpBC,UAAU,EACVC,kBAAkB,QACb,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGjB,UAAU;AAClC,MAAM;EAAEkB;AAAO,CAAC,GAAG1B,MAAM;;AAEzB;AACA,MAAM2B,YAAY,GAAG;EACnBC,MAAM,EAAE;IACNC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,WAAW;IACxBC,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC;IAC3DC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO;EACzC,CAAC;EACDC,MAAM,EAAE;IACNN,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,oBAAoB;IACjCC,MAAM,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,wBAAwB,CAAC;IACzFC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO;EACzC,CAAC;EACDE,KAAK,EAAE;IACLP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,WAAW;IACxBC,MAAM,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,CAAC;IAC/CC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO;EACzC,CAAC;EACDG,WAAW,EAAE;IACXR,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,WAAW;IACxBC,MAAM,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,UAAU,CAAC;IACnDC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO;EACzC,CAAC;EACDI,MAAM,EAAE;IACNT,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,iBAAiB;IAC9BC,MAAM,EAAE,CAAC,YAAY,EAAE,mBAAmB,EAAE,cAAc,CAAC;IAC3DC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO;EACzC,CAAC;EACDK,IAAI,EAAE;IACJV,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,YAAY;IACzBC,MAAM,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC/BC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO;EACzC,CAAC;EACDM,MAAM,EAAE;IACNX,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,UAAU;IACvBC,MAAM,EAAE,CAAC,0BAA0B,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,CAAC;IAC9EC,MAAM,EAAE,CAAC,UAAU,EAAE,OAAO;EAC9B,CAAC;EACDO,MAAM,EAAE;IACNZ,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,YAAY;IACzBC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO;EACzC;AACF,CAAC;AAED,MAAMQ,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,IAAI,CAAC,GAAG9C,IAAI,CAAC+C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,QAAQ,CAAC;EAChE,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAC;IAAEyD,SAAS,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAU,CAAC,CAAC;EACjF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAMiE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFD,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAME,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,0BAA0B,CAAC;MAC5DC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,QAAQ,CAACI,IAAI,CAAC;MAErD,MAAMA,IAAI,GAAGJ,QAAQ,CAACI,IAAI;MAE1B,IAAIA,IAAI,CAACZ,MAAM,KAAK,OAAO,EAAE;QAAA,IAAAa,iBAAA;QAC3B;QACAvD,OAAO,CAACwD,KAAK,CAAC;UACZC,OAAO,eACL7C,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAA8C,QAAA,EAAK;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrBlD,OAAA;cAAKmD,KAAK,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAM,CAAE;cAAAP,QAAA,GAAAH,iBAAA,GAChDD,IAAI,CAACY,WAAW,cAAAX,iBAAA,uBAAhBA,iBAAA,CAAkBY,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK,kBACnD1D,OAAA;gBAAA8C,QAAA,GAAiB,SAAE,EAACW,UAAU;cAAA,GAApBC,KAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqB,CACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;UACDS,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFzB,eAAe,CAAC,EAAE,CAAC;QACnB9B,YAAY,CAACa,MAAM,CAACP,MAAM,GAAG,EAAE;QAC/B,OAAO,EAAE;MACX;MAEA,MAAMA,MAAM,GAAGgC,IAAI,CAAChC,MAAM,CAAC8C,GAAG,CAACI,KAAK,KAAK;QACvCtD,IAAI,EAAEsD,KAAK,CAACtD,IAAI;QAChBuD,IAAI,EAAED,KAAK,CAACC,IAAI;QAChBC,WAAW,EAAEF,KAAK,CAACE;MACrB,CAAC,CAAC,CAAC;MAEH5B,eAAe,CAACxB,MAAM,CAAC;;MAEvB;MACAN,YAAY,CAACa,MAAM,CAACP,MAAM,GAAGA,MAAM,CAAC8C,GAAG,CAACI,KAAK,IAAIA,KAAK,CAACtD,IAAI,CAAC;MAE5D,IAAII,MAAM,CAACqD,MAAM,KAAK,CAAC,EAAE;QACvB3E,OAAO,CAAC4E,IAAI,CAAC;UACXnB,OAAO,eACL7C,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAA8C,QAAA,EAAK;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzBlD,OAAA;cAAKmD,KAAK,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAM,CAAE;cAAAP,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;UACDS,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLvE,OAAO,CAAC6E,OAAO,CAAC,OAAOvD,MAAM,CAACqD,MAAM,QAAQ,CAAC;MAC/C;MAEA,OAAOrD,MAAM;IACf,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCxD,OAAO,CAACwD,KAAK,CAAC;QACZC,OAAO,eACL7C,OAAA;UAAA8C,QAAA,gBACE9C,OAAA;YAAA8C,QAAA,EAAK;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBlD,OAAA;YAAKmD,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEC,SAAS,EAAE;YAAM,CAAE;YAAAP,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;QACDS,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFzB,eAAe,CAAC,EAAE,CAAC;MACnB9B,YAAY,CAACa,MAAM,CAACP,MAAM,GAAG,EAAE;MAC/B,OAAO,EAAE;IACX,CAAC,SAAS;MACR0B,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM8B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF9B,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAME,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,mCAAmC,CAAC;MACrE,MAAMG,IAAI,GAAGJ,QAAQ,CAACI,IAAI;MAE1B,IAAIA,IAAI,CAACb,SAAS,EAAE;QAClBzC,OAAO,CAAC6E,OAAO,CAAC;UACdpB,OAAO,eACL7C,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAA8C,QAAA,EAAK;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrBlD,OAAA;cAAKmD,KAAK,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAM,CAAE;cAAAP,QAAA,GAAC,qBAC9C,EAACJ,IAAI,CAACyB,YAAY,EAAC,qBACzB;YAAA;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;UACDS,QAAQ,EAAE;QACZ,CAAC,CAAC;;QAEF;QACA,MAAMtB,iBAAiB,CAAC,CAAC;MAC3B,CAAC,MAAM;QAAA,IAAA+B,kBAAA;QACLhF,OAAO,CAACwD,KAAK,CAAC;UACZC,OAAO,eACL7C,OAAA;YAAA8C,QAAA,gBACE9C,OAAA;cAAA8C,QAAA,EAAK;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrBlD,OAAA;cAAKmD,KAAK,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAM,CAAE;cAAAP,QAAA,GAAAsB,kBAAA,GAChD1B,IAAI,CAACY,WAAW,cAAAc,kBAAA,uBAAhBA,kBAAA,CAAkBb,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK,kBACnD1D,OAAA;gBAAA8C,QAAA,GAAiB,SAAE,EAACW,UAAU;cAAA,GAApBC,KAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqB,CACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;UACDS,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCxD,OAAO,CAACwD,KAAK,CAAC;QACZC,OAAO,eACL7C,OAAA;UAAA8C,QAAA,gBACE9C,OAAA;YAAA8C,QAAA,EAAK;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjBlD,OAAA;YAAKmD,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEC,SAAS,EAAE;YAAM,CAAE;YAAAP,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;QACDS,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRvB,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMiC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B7B,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,IAAI;MACF,MAAM,CAAC6B,YAAY,EAAEC,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClD3E,KAAK,CAACyC,GAAG,CAAC,mBAAmB,CAAC,EAC9BzC,KAAK,CAACyC,GAAG,CAAC,gBAAgB,CAAC,CAC5B,CAAC;MAEFC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6B,YAAY,CAAC5B,IAAI,CAAC;MACrDF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE8B,SAAS,CAAC7B,IAAI,CAAC;MAE/CV,YAAY,CAACsC,YAAY,CAAC5B,IAAI,CAACX,SAAS,CAAC;MACzCL,kBAAkB,CAAC4C,YAAY,CAAC5B,IAAI,CAACgC,OAAO,CAAC;MAC7C9C,WAAW,CAAC2C,SAAS,CAAC7B,IAAI,CAAC;;MAE3B;MACA,IAAI4B,YAAY,CAAC5B,IAAI,CAACgC,OAAO,KAAK,QAAQ,EAAE;QAC1C,MAAMrC,iBAAiB,CAAC,CAAC;MAC3B;;MAEA;MACA,IAAIiC,YAAY,CAAC5B,IAAI,CAACgC,OAAO,EAAE;QAC7B,IAAI;UACF,MAAMC,SAAS,GAAG,MAAM7E,KAAK,CAACyC,GAAG,CAAC,qBAAqB+B,YAAY,CAAC5B,IAAI,CAACgC,OAAO,EAAE,CAAC;UACnFlC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkC,SAAS,CAACjC,IAAI,CAAC;UACvDrB,IAAI,CAACuD,cAAc,CAACD,SAAS,CAACjC,IAAI,CAACmC,MAAM,CAAC;QAC5C,CAAC,CAAC,OAAOjC,KAAK,EAAE;UACdJ,OAAO,CAACsC,IAAI,CAAC,SAAS,EAAElC,KAAK,CAAC;QAChC;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCxD,OAAO,CAACwD,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMmC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFvD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,mBAAmB,CAAC;MACrDX,WAAW,CAACU,QAAQ,CAACI,IAAI,CAAC;MAE1B,IAAIJ,QAAQ,CAACI,IAAI,CAACb,SAAS,EAAE;QAC3BzC,OAAO,CAAC6E,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL7E,OAAO,CAAC4F,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdxD,OAAO,CAACwD,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyD,cAAc,GAAG,MAAOC,QAAQ,IAAK;IACzC1C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyC,QAAQ,CAAC;IAC/C,IAAI;MAAA,IAAAC,qBAAA;MACF3D,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMc,QAAQ,GAAG,MAAMxC,KAAK,CAACsF,IAAI,CAAC,4BAA4B,EAAE;QAAEF;MAAS,CAAC,CAAC;MAC7E1C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEH,QAAQ,CAACI,IAAI,CAAC;MAEvDhB,kBAAkB,CAACwD,QAAQ,CAAC;MAC5B9F,OAAO,CAAC6E,OAAO,CAAC,QAAQ,EAAAkB,qBAAA,GAAA/E,YAAY,CAAC8E,QAAQ,CAAC,cAAAC,qBAAA,uBAAtBA,qBAAA,CAAwB7E,IAAI,KAAI4E,QAAQ,EAAE,CAAC;;MAEnE;MACA,IAAIA,QAAQ,KAAK,QAAQ,EAAE;QACzB,MAAM7C,iBAAiB,CAAC,CAAC;MAC3B;;MAEA;MACA,IAAI;QACF,MAAMsC,SAAS,GAAG,MAAM7E,KAAK,CAACyC,GAAG,CAAC,kBAAkB2C,QAAQ,EAAE,CAAC;QAC/D1C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkC,SAAS,CAACjC,IAAI,CAAC;QAC7CrB,IAAI,CAACuD,cAAc,CAACD,SAAS,CAACjC,IAAI,CAACmC,MAAM,CAAC;MAC5C,CAAC,CAAC,OAAOjC,KAAK,EAAE;QACdJ,OAAO,CAACsC,IAAI,CAAC,SAAS,EAAElC,KAAK,CAAC;QAC9B;QACA,MAAMyC,aAAa,GAAG;UACpBC,UAAU,EAAE,IAAI;UAChBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE;QACX,CAAC;;QAED;QACA,IAAIN,QAAQ,KAAK,QAAQ,EAAE;UACzBG,aAAa,CAACzB,KAAK,GAAG,0BAA0B;UAChDyB,aAAa,CAACI,QAAQ,GAAG,wBAAwB;QACnD;QAEAjD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4C,aAAa,CAAC;QACrDhE,IAAI,CAACuD,cAAc,CAACS,aAAa,CAAC;MACpC;MAEA,MAAMhB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MAAA,IAAA8C,eAAA,EAAAC,oBAAA;MACdnD,OAAO,CAACI,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCxD,OAAO,CAACwD,KAAK,CAAC,YAAY,EAAA8C,eAAA,GAAA9C,KAAK,CAACN,QAAQ,cAAAoD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhD,IAAI,cAAAiD,oBAAA,uBAApBA,oBAAA,CAAsBC,MAAM,KAAIhD,KAAK,CAACxD,OAAO,EAAE,CAAC;IAC5E,CAAC,SAAS;MACRoC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqE,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAI;MACFtE,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM1B,KAAK,CAACsF,IAAI,CAAC,gBAAgB,EAAE;QACjCF,QAAQ,EAAEzD,eAAe;QACzBoD,MAAM,EAAEiB;MACV,CAAC,CAAC;MACF1G,OAAO,CAAC6E,OAAO,CAAC,QAAQ,CAAC;MACzB,MAAMI,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxD,OAAO,CAACwD,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDnD,SAAS,CAAC,MAAM;IACdgG,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0B,YAAY,GAAGA,CAAC;IAAEb,QAAQ;IAAEc,QAAQ;IAAEC,OAAO;IAAEC,QAAQ,GAAG;EAAM,CAAC,KAAK;IAC1E,MAAMrB,MAAM,GAAGzE,YAAY,CAAC8E,QAAQ,CAAC;IAErC,MAAMiB,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAID,QAAQ,EAAE;MACd1D,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyC,QAAQ,CAAC;MAC/Ce,OAAO,CAACf,QAAQ,CAAC;IACnB,CAAC;IAED,oBACElF,OAAA,CAAC1B,IAAI;MACHuF,IAAI,EAAC,OAAO;MACZuC,SAAS,EAAE,CAACF,QAAS;MACrBG,SAAS,EAAE,iBAAiBL,QAAQ,GAAG,QAAQ,GAAG,EAAE,IAAIE,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;MACrFD,OAAO,EAAEE,WAAY;MACrBhD,KAAK,EAAE;QACLmD,WAAW,EAAEN,QAAQ,GAAGnB,MAAM,CAACrE,KAAK,GAAG,SAAS;QAChD+F,eAAe,EAAEP,QAAQ,GAAG,GAAGnB,MAAM,CAACrE,KAAK,IAAI,GAAG0F,QAAQ,GAAG,SAAS,GAAG,MAAM;QAC/EM,MAAM,EAAEN,QAAQ,GAAG,aAAa,GAAG,SAAS;QAC5CO,UAAU,EAAE,UAAU;QACtBC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAEX,QAAQ,GAAG,aAAanB,MAAM,CAACrE,KAAK,EAAE,GAAG,mBAAmB;QACpEoG,OAAO,EAAEV,QAAQ,GAAG,GAAG,GAAG;MAC5B,CAAE;MAAApD,QAAA,eAEF9C,OAAA;QAAKmD,KAAK,EAAE;UAAE0D,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAM,CAAE;QAAAhE,QAAA,gBAClD9C,OAAA;UAAKmD,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAE2D,YAAY,EAAE;UAAM,CAAE;UAAAjE,QAAA,EACnD+B,MAAM,CAACtE;QAAI;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNlD,OAAA;UAAKmD,KAAK,EAAE;YACV6D,UAAU,EAAE,MAAM;YAClBxG,KAAK,EAAE0F,QAAQ,GAAG,MAAM,GAAGrB,MAAM,CAACrE,KAAK;YACvCuG,YAAY,EAAE;UAChB,CAAE;UAAAjE,QAAA,EACC+B,MAAM,CAACvE;QAAI;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNlD,OAAA;UAAKmD,KAAK,EAAE;YACVC,QAAQ,EAAE,MAAM;YAChB5C,KAAK,EAAE0F,QAAQ,GAAG,MAAM,GAAG,MAAM;YACjCe,UAAU,EAAE;UACd,CAAE;UAAAnE,QAAA,EACC+B,MAAM,CAACpE;QAAW;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,EACL8C,QAAQ,IAAI,CAACE,QAAQ,iBACpBlG,OAAA;UAAKmD,KAAK,EAAE;YAAEE,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC/B9C,OAAA,CAACpB,GAAG;YAAC4B,KAAK,EAAEqE,MAAM,CAACrE,KAAM;YAACqD,IAAI,EAAC,OAAO;YAAAf,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CACN,EACAgD,QAAQ,iBACPlG,OAAA;UAAKmD,KAAK,EAAE;YAAEE,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC/B9C,OAAA,CAACpB,GAAG;YAAC4B,KAAK,EAAC,SAAS;YAACqD,IAAI,EAAC,OAAO;YAAAf,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;;EAED;EACA,MAAMgE,eAAe,GAAGA,CAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,oBACtBrH,OAAA;MAAKmD,KAAK,EAAE;QAAE0D,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAhE,QAAA,gBACnD9C,OAAA;QAAKmD,KAAK,EAAE;UAAE4D,YAAY,EAAE;QAAO,CAAE;QAAAjE,QAAA,eACnC9C,OAAA,CAACpB,GAAG;UACF4B,KAAK,EAAEmB,QAAQ,CAACE,SAAS,GAAG,OAAO,GAAG,KAAM;UAC5CtB,IAAI,EAAEoB,QAAQ,CAACE,SAAS,gBAAG7B,OAAA,CAACT,mBAAmB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACR,yBAAyB;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnFC,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAE0D,OAAO,EAAE;UAAW,CAAE;UAAAhE,QAAA,EAEhDnB,QAAQ,CAACE,SAAS,GAAG,IAAI,GAAG;QAAI;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlD,OAAA;QAAKmD,KAAK,EAAE;UAAE4D,YAAY,EAAE;QAAO,CAAE;QAAAjE,QAAA,gBACnC9C,OAAA,CAACE,IAAI;UAACoH,IAAI,EAAC,WAAW;UAAAxE,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnClD,OAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlD,OAAA,CAACE,IAAI;UAACqH,MAAM;UAACpE,KAAK,EAAE;YAAE3C,KAAK,GAAA2G,qBAAA,GAAE/G,YAAY,CAACqB,eAAe,CAAC,cAAA0F,qBAAA,uBAA7BA,qBAAA,CAA+B3G;UAAM,CAAE;UAAAsC,QAAA,IAAAsE,sBAAA,GACjEhH,YAAY,CAACqB,eAAe,CAAC,cAAA2F,sBAAA,uBAA7BA,sBAAA,CAA+B7G,IAAI,EAAC,GAAC,GAAA8G,sBAAA,GAACjH,YAAY,CAACqB,eAAe,CAAC,cAAA4F,sBAAA,uBAA7BA,sBAAA,CAA+B/G,IAAI;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACtB,MAAM;QACL4I,IAAI,EAAC,SAAS;QACd/G,IAAI,eAAEP,OAAA,CAACP,cAAc;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzB+C,OAAO,EAAElB,cAAe;QACxBxD,OAAO,EAAEA,OAAQ;QACjBsC,IAAI,EAAC,OAAO;QAAAf,QAAA,EACb;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,CACP;EAED,MAAMsE,aAAa,GAAGpH,YAAY,CAACqB,eAAe,CAAC;EAEnD,oBACEzB,OAAA;IAAKmD,KAAK,EAAE;MAAEsE,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAA5E,QAAA,gBAEnD9C,OAAA,CAAC1B,IAAI;MAAC6E,KAAK,EAAE;QAAE4D,YAAY,EAAE;MAAO,CAAE;MAAAjE,QAAA,eACpC9C,OAAA,CAAClB,GAAG;QAAC6I,MAAM,EAAE,EAAG;QAACC,KAAK,EAAC,QAAQ;QAAA9E,QAAA,gBAC7B9C,OAAA,CAACjB,GAAG;UAAC8I,IAAI,EAAE,EAAG;UAAA/E,QAAA,eACZ9C,OAAA,CAACrB,KAAK;YAACkF,IAAI,EAAC,OAAO;YAAAf,QAAA,gBACjB9C,OAAA;cAAA8C,QAAA,eACE9C,OAAA,CAACV,aAAa;gBAAC6D,KAAK,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAE5C,KAAK,EAAEgH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEhH;gBAAM;cAAE;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACNlD,OAAA;cAAA8C,QAAA,gBACE9C,OAAA,CAACC,KAAK;gBAAC6H,KAAK,EAAE,CAAE;gBAAC3E,KAAK,EAAE;kBAAEuE,MAAM,EAAE;gBAAE,CAAE;gBAAA5E,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDlD,OAAA,CAACE,IAAI;gBAACoH,IAAI,EAAC,WAAW;gBAAAxE,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlD,OAAA,CAACjB,GAAG;UAAC8I,IAAI,EAAE,CAAE;UAAA/E,QAAA,eACX9C,OAAA,CAACkH,eAAe;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPlD,OAAA,CAAClB,GAAG;MAAC6I,MAAM,EAAE,EAAG;MAAA7E,QAAA,gBAEd9C,OAAA,CAACjB,GAAG;QAAC8I,IAAI,EAAE,EAAG;QAAA/E,QAAA,gBACZ9C,OAAA,CAAC1B,IAAI;UACHyJ,KAAK,eACH/H,OAAA,CAACrB,KAAK;YAAAmE,QAAA,GAAC,kCAEL,eAAA9C,OAAA,CAACpB,GAAG;cAAC4B,KAAK,EAAC,MAAM;cAAAsC,QAAA,GAAEkF,MAAM,CAACC,IAAI,CAAC7H,YAAY,CAAC,CAAC2D,MAAM,EAAC,qBAAI;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACR;UACDC,KAAK,EAAE;YAAE4D,YAAY,EAAE;UAAO,CAAE;UAChCmB,KAAK,eACHlI,OAAA,CAACtB,MAAM;YACLmF,IAAI,EAAC,OAAO;YACZoC,OAAO,EAAE5B,WAAY;YACrB9C,OAAO,EAAEA,OAAQ;YACjBhB,IAAI,eAAEP,OAAA,CAACP,cAAc;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAC1B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAJ,QAAA,EAEAvB,OAAO,gBACNvB,OAAA;YAAKmD,KAAK,EAAE;cAAE0D,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAAhE,QAAA,eACnD9C,OAAA,CAACrB,KAAK;cAACwJ,SAAS,EAAC,UAAU;cAAArF,QAAA,eACzB9C,OAAA;gBAAA8C,QAAA,EAAK;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,gBAENlD,OAAA,CAAClB,GAAG;YAAC6I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAA7E,QAAA,EACnBkF,MAAM,CAACC,IAAI,CAAC7H,YAAY,CAAC,CAACoD,GAAG,CAAC0B,QAAQ,iBACrClF,OAAA,CAACjB,GAAG;cAAC8I,IAAI,EAAE,CAAE;cAAA/E,QAAA,eACX9C,OAAA,CAAC+F,YAAY;gBACXb,QAAQ,EAAEA,QAAS;gBACnBc,QAAQ,EAAEvE,eAAe,KAAKyD,QAAS;gBACvCe,OAAO,EAAEhB,cAAe;gBACxBiB,QAAQ,EAAE3E;cAAQ;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC,GANegC,QAAQ;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOtB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGPlD,OAAA,CAAC1B,IAAI;UAACyJ,KAAK,EAAE,GAAGP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAElH,IAAI,KAAM;UAAAwC,QAAA,eACvC9C,OAAA,CAACzB,IAAI;YACH8C,IAAI,EAAEA,IAAK;YACX+G,MAAM,EAAC,UAAU;YACjBC,QAAQ,EAAExC,UAAW;YACrByC,aAAa,EAAE;cACbhD,UAAU,EAAE,IAAI;cAChBC,WAAW,EAAE,GAAG;cAChBC,OAAO,EAAE,IAAI;cACb5B,KAAK,EAAEnC,eAAe,KAAK,QAAQ,GAAG,0BAA0B,GAAG8G,SAAS;cAC5E9C,QAAQ,EAAEhE,eAAe,KAAK,QAAQ,GAAG,wBAAwB,GAAG8G;YACtE,CAAE;YAAAzF,QAAA,GAGD,CAAA0E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE7G,MAAM,CAAC6H,QAAQ,CAAC,SAAS,CAAC,kBACxCxI,OAAA,CAACzB,IAAI,CAACkK,IAAI;cACRnI,IAAI,EAAC,SAAS;cACdoI,KAAK,eACH1I,OAAA,CAACrB,KAAK;gBAAAmE,QAAA,GAAC,SAEL,eAAA9C,OAAA,CAACX,OAAO;kBAAC0I,KAAK,EAAC,iEAAe;kBAAAjF,QAAA,eAC5B9C,OAAA,CAACH,kBAAkB;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACR;cAAAJ,QAAA,eAED9C,OAAA,CAACxB,KAAK,CAACmK,QAAQ;gBACbC,WAAW,EAAE,MAAMpB,aAAa,CAAClH,IAAI,UAAW;gBAChDuI,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAG9I,OAAA,CAACJ,UAAU;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACL,oBAAoB;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAG;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CACZ,EAGA,CAAAsE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE7G,MAAM,CAAC6H,QAAQ,CAAC,UAAU,CAAC,kBACzCxI,OAAA,CAACzB,IAAI,CAACkK,IAAI;cACRnI,IAAI,EAAC,UAAU;cACfoI,KAAK,EAAC,iBAAO;cAAA5F,QAAA,eAEb9C,OAAA,CAACxB,KAAK;gBAACoK,WAAW,EAAC;cAAS;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CACZ,EAGA,CAAAsE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE7G,MAAM,CAAC6H,QAAQ,CAAC,OAAO,CAAC,kBACtCxI,OAAA,CAACzB,IAAI,CAACkK,IAAI;cACRnI,IAAI,EAAC,OAAO;cACZoI,KAAK,eACH1I,OAAA,CAACrB,KAAK;gBAAAmE,QAAA,GAAC,cAEL,EAACrB,eAAe,KAAK,QAAQ,iBAC3BzB,OAAA,CAACrB,KAAK;kBAAAmE,QAAA,gBACJ9C,OAAA,CAACtB,MAAM;oBACL4I,IAAI,EAAC,MAAM;oBACXzD,IAAI,EAAC,OAAO;oBACZtD,IAAI,eAAEP,OAAA,CAACP,cAAc;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzB+C,OAAO,EAAE5D,iBAAkB;oBAC3Bd,OAAO,EAAEY,mBAAoB;oBAC7BgB,KAAK,EAAE;sBAAE2D,OAAO,EAAE;oBAAE,CAAE;oBAAAhE,QAAA,EACvB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlD,OAAA,CAACtB,MAAM;oBACL4I,IAAI,EAAC,MAAM;oBACXzD,IAAI,EAAC,OAAO;oBACZoC,OAAO,EAAE/B,oBAAqB;oBAC9Bf,KAAK,EAAE;sBAAE2D,OAAO,EAAE;oBAAE,CAAE;oBAAAhE,QAAA,EACvB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACR;cAAAJ,QAAA,eAED9C,OAAA,CAACvB,MAAM;gBACLmK,WAAW,EAAEnH,eAAe,KAAK,QAAQ,GAAG,QAAQ,GAAG,MAAO;gBAC9DF,OAAO,EAAEE,eAAe,KAAK,QAAQ,IAAIU,mBAAoB;gBAC7D4G,eAAe,EACbtH,eAAe,KAAK,QAAQ,GACvBU,mBAAmB,GAAG,QAAQ,GAAG,gBAAgB,GAClD,OACL;gBAAAW,QAAA,EAEArB,eAAe,KAAK,QAAQ,GAC3BQ,YAAY,CAACuB,GAAG,CAACI,KAAK,iBACpB5D,OAAA,CAACG,MAAM;kBAAkB6I,KAAK,EAAEpF,KAAK,CAACtD,IAAK;kBAAAwC,QAAA,eACzC9C,OAAA;oBAAKmD,KAAK,EAAE;sBAAE8F,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEC,UAAU,EAAE;oBAAS,CAAE;oBAAArG,QAAA,gBACrF9C,OAAA;sBAAA8C,QAAA,EAAOc,KAAK,CAACtD;oBAAI;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzBlD,OAAA,CAACE,IAAI;sBAACoH,IAAI,EAAC,WAAW;sBAACnE,KAAK,EAAE;wBAAEC,QAAQ,EAAE;sBAAO,CAAE;sBAAAN,QAAA,GAChD,CAACc,KAAK,CAACC,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,EAAEuF,OAAO,CAAC,CAAC,CAAC,EAAC,IAClD;oBAAA;sBAAArG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC,GANKU,KAAK,CAACtD,IAAI;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOf,CACT,CAAC,GAEFsE,aAAa,CAAC9G,MAAM,CAAC8C,GAAG,CAACI,KAAK,iBAC5B5D,OAAA,CAACG,MAAM;kBAAa6I,KAAK,EAAEpF,KAAM;kBAAAd,QAAA,EAAEc;gBAAK,GAA3BA,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CAClD;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACZ,eAEDlD,OAAA,CAAChB,OAAO;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGXlD,OAAA,CAAClB,GAAG;cAAC6I,MAAM,EAAE,EAAG;cAAA7E,QAAA,gBACd9C,OAAA,CAACjB,GAAG;gBAAC8I,IAAI,EAAE,CAAE;gBAAA/E,QAAA,eACX9C,OAAA,CAACzB,IAAI,CAACkK,IAAI;kBAACnI,IAAI,EAAC,YAAY;kBAACoI,KAAK,EAAC,yBAAU;kBAAA5F,QAAA,eAC3C9C,OAAA,CAACb,WAAW;oBACVkK,GAAG,EAAE,GAAI;oBACTC,GAAG,EAAE,IAAK;oBACVnG,KAAK,EAAE;sBAAEoG,KAAK,EAAE;oBAAO,CAAE;oBACzBX,WAAW,EAAC;kBAAM;oBAAA7F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlD,OAAA,CAACjB,GAAG;gBAAC8I,IAAI,EAAE,CAAE;gBAAA/E,QAAA,eACX9C,OAAA,CAACzB,IAAI,CAACkK,IAAI;kBAACnI,IAAI,EAAC,aAAa;kBAACoI,KAAK,EAAC,0BAAM;kBAAA5F,QAAA,eACxC9C,OAAA,CAACb,WAAW;oBACVkK,GAAG,EAAE,CAAE;oBACPC,GAAG,EAAE,CAAE;oBACPE,IAAI,EAAE,GAAI;oBACVrG,KAAK,EAAE;sBAAEoG,KAAK,EAAE;oBAAO,CAAE;oBACzBX,WAAW,EAAC;kBAAK;oBAAA7F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlD,OAAA,CAACjB,GAAG;gBAAC8I,IAAI,EAAE,CAAE;gBAAA/E,QAAA,eACX9C,OAAA,CAACzB,IAAI,CAACkK,IAAI;kBAACnI,IAAI,EAAC,SAAS;kBAACoI,KAAK,EAAC,cAAI;kBAACe,aAAa,EAAC,SAAS;kBAAA3G,QAAA,eAC1D9C,OAAA,CAACd,MAAM;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlD,OAAA,CAACzB,IAAI,CAACkK,IAAI;cAAA3F,QAAA,eACR9C,OAAA,CAACtB,MAAM;gBACL4I,IAAI,EAAC,SAAS;gBACdoC,QAAQ,EAAC,QAAQ;gBACjBnJ,IAAI,eAAEP,OAAA,CAACN,YAAY;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvB3B,OAAO,EAAEA,OAAQ;gBACjBsC,IAAI,EAAC,OAAO;gBAAAf,QAAA,EACb;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlD,OAAA,CAACjB,GAAG;QAAC8I,IAAI,EAAE,CAAE;QAAA/E,QAAA,gBACX9C,OAAA,CAAC1B,IAAI;UAACyJ,KAAK,EAAC,0BAAM;UAAClE,IAAI,EAAC,OAAO;UAACV,KAAK,EAAE;YAAE4D,YAAY,EAAE;UAAO,CAAE;UAAAjE,QAAA,eAC9D9C,OAAA;YAAKmD,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAE6D,UAAU,EAAE;YAAM,CAAE;YAAAnE,QAAA,gBAClD9C,OAAA;cAAA8C,QAAA,eAAG9C,OAAA;gBAAA8C,QAAA,GAAS0E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEjH,IAAI,EAAC,GAAC,EAACiH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAElH,IAAI;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnElD,OAAA;cAAA8C,QAAA,EAAI0E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE/G;YAAW;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAElCzB,eAAe,KAAK,QAAQ,iBAC3BzB,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,GAAG,sBAAK,eAAA9C,OAAA;kBAAG2J,IAAI,EAAC,8BAA8B;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAA/G,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wBAAU;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnHlD,OAAA;gBAAA8C,QAAA,EAAG;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CACN,EAEAzB,eAAe,KAAK,QAAQ,iBAC3BzB,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,GAAG,sBAAK,eAAA9C,OAAA;kBAAG2J,IAAI,EAAC,gCAAgC;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAA/G,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wBAAU;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxHlD,OAAA;gBAAA8C,QAAA,EAAG;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CACN,EAEAzB,eAAe,KAAK,OAAO,iBAC1BzB,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,GAAG,sBAAK,eAAA9C,OAAA;kBAAG2J,IAAI,EAAC,2BAA2B;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAA/G,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wBAAU;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9GlD,OAAA;gBAAA8C,QAAA,EAAG;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CACN,EAEAzB,eAAe,KAAK,aAAa,iBAChCzB,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,GAAG,sBAAK,eAAA9C,OAAA;kBAAG2J,IAAI,EAAC,yBAAyB;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAA/G,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wBAAU;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5GlD,OAAA;gBAAA8C,QAAA,EAAG;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClBlD,OAAA;gBAAA8C,QAAA,EAAG;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACN,EAEAzB,eAAe,KAAK,QAAQ,iBAC3BzB,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,GAAG,sBAAK,eAAA9C,OAAA;kBAAG2J,IAAI,EAAC,wBAAwB;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAA/G,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wBAAU;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrHlD,OAAA;gBAAA8C,QAAA,EAAG;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrBlD,OAAA;gBAAA8C,QAAA,EAAG;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACN,EAEAzB,eAAe,KAAK,MAAM,iBACzBzB,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,GAAG,sBAAK,eAAA9C,OAAA;kBAAG2J,IAAI,EAAC,eAAe;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAA/G,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wBAAU;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjGlD,OAAA;gBAAA8C,QAAA,EAAG;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnBlD,OAAA;gBAAA8C,QAAA,EAAG;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACN,EAEAzB,eAAe,KAAK,QAAQ,iBAC3BzB,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,EAAG;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACvBlD,OAAA;gBAAA8C,QAAA,EAAG;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjBlD,OAAA;gBAAA8C,QAAA,EAAG;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChBlD,OAAA;gBAAA8C,QAAA,GAAG,mCAAQ,eAAA9C,OAAA;kBAAG2J,IAAI,EAAC,oBAAoB;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAA/G,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnGlD,OAAA,CAAChB,OAAO;gBAACmE,KAAK,EAAE;kBAAEuE,MAAM,EAAE;gBAAQ;cAAE;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvClD,OAAA;gBAAA8C,QAAA,eAAG9C,OAAA;kBAAA8C,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAC9Bf,mBAAmB,gBAClBnC,OAAA;gBAAA8C,QAAA,EAAG;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,GAChBjB,YAAY,CAAC8B,MAAM,GAAG,CAAC,gBACzB/D,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAA8C,QAAA,GAAG,4BAAM,EAACb,YAAY,CAAC8B,MAAM,EAAC,qBAAI;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACrCjB,YAAY,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACI,KAAK,iBACjC5D,OAAA;kBAAoBmD,KAAK,EAAE;oBAAEC,QAAQ,EAAE,MAAM;oBAAEsE,MAAM,EAAE;kBAAQ,CAAE;kBAAA5E,QAAA,GAAC,IAC9D,EAACc,KAAK,CAACtD,IAAI,EAAC,IAAE,EAAC,CAACsD,KAAK,CAACC,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,EAAEuF,OAAO,CAAC,CAAC,CAAC,EAAC,KAClE;gBAAA,GAFQxF,KAAK,CAACtD,IAAI;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACJ,CAAC,EACDjB,YAAY,CAAC8B,MAAM,GAAG,CAAC,iBACtB/D,OAAA;kBAAGmD,KAAK,EAAE;oBAAEC,QAAQ,EAAE,MAAM;oBAAEsE,MAAM,EAAE;kBAAQ,CAAE;kBAAA5E,QAAA,GAAC,mBACxC,EAACb,YAAY,CAAC8B,MAAM,GAAG,CAAC,EAAC,qBAClC;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,gBAENlD,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAA8C,QAAA,EAAG;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjBlD,OAAA;kBAAGmD,KAAK,EAAE;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAN,QAAA,EAAC;gBAAiC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,EAEAzB,eAAe,KAAK,QAAQ,iBAC3BzB,OAAA;cAAA8C,QAAA,gBACE9C,OAAA;gBAAA8C,QAAA,EAAG;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACzBlD,OAAA;gBAAA8C,QAAA,EAAG;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnBlD,OAAA;gBAAA8C,QAAA,EAAG;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAENvB,QAAQ,CAACiB,KAAK,iBACb5C,OAAA,CAACnB,KAAK;UACJO,OAAO,EAAC,0BAAM;UACdqB,WAAW,EAAEkB,QAAQ,CAACiB,KAAM;UAC5B0E,IAAI,EAAC,OAAO;UACZzD,IAAI,EAAC,OAAO;UACZV,KAAK,EAAE;YAAE4D,YAAY,EAAE;UAAO;QAAE;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CACF,eAEDlD,OAAA,CAAC1B,IAAI;UAACyJ,KAAK,EAAC,0BAAM;UAAClE,IAAI,EAAC,OAAO;UAAAf,QAAA,eAC7B9C,OAAA,CAACrB,KAAK;YAACwJ,SAAS,EAAC,UAAU;YAAChF,KAAK,EAAE;cAAEoG,KAAK,EAAE;YAAO,CAAE;YAAAzG,QAAA,gBACnD9C,OAAA,CAACtB,MAAM;cAACoL,KAAK;cAAC7D,OAAO,EAAE5B,WAAY;cAAC9C,OAAO,EAAEA,OAAQ;cAAAuB,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlD,OAAA,CAACtB,MAAM;cAACoL,KAAK;cAAC7D,OAAO,EAAElB,cAAe;cAACxD,OAAO,EAAEA,OAAQ;cAAAuB,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRzB,eAAe,KAAK,QAAQ,iBAC3BzB,OAAA,CAACtB,MAAM;cACLoL,KAAK;cACL7D,OAAO,EAAE5D,iBAAkB;cAC3Bd,OAAO,EAAEY,mBAAoB;cAC7B5B,IAAI,eAAEP,OAAA,CAACP,cAAc;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CArsBID,aAAa;EAAA,QACF5C,IAAI,CAAC+C,OAAO;AAAA;AAAAyI,EAAA,GADvB5I,aAAa;AAusBnB,eAAeA,aAAa;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}