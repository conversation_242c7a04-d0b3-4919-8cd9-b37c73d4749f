import React, { useState } from 'react';
import { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space, Collapse } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ProjectOutlined,
  UserOutlined,
  TeamOutlined,
  BookOutlined,
  FileTextOutlined,
  GlobalOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  ShareAltOutlined,
  RobotOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  BellOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  ShoppingOutlined,
  HeartOutlined,
  CompassOutlined,
  StarOutlined,
  CrownOutlined,
  BankOutlined,
  DownOutlined,
  RightOutlined
} from '@ant-design/icons';

const { Header, Sider, Content } = AntLayout;
const { Panel } = Collapse;

const Layout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [contentCollapsed, setContentCollapsed] = useState(false);
  const [settingsCollapsed, setSettingsCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // 获取当前项目ID（如果在项目页面中）
  const getProjectId = () => {
    const pathParts = location.pathname.split('/');
    if (pathParts[1] === 'projects' && pathParts[2]) {
      return pathParts[2];
    }
    return null;
  };

  const projectId = getProjectId();

  // 基础菜单项
  const baseMenuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: '项目管理',
    },
  ];

  // 内容管理菜单项
  const contentMenuItems = projectId ? [
    {
      key: `/projects/${projectId}/characters`,
      icon: <UserOutlined />,
      label: '人物管理',
    },
    {
      key: `/projects/${projectId}/factions`,
      icon: <TeamOutlined />,
      label: '势力管理',
    },
    {
      key: `/projects/${projectId}/plots`,
      icon: <BookOutlined />,
      label: '剧情管理',
    },
    {
      key: `/projects/${projectId}/volumes`,
      icon: <FileTextOutlined />,
      label: '卷宗管理',
    },
    {
      key: `/projects/${projectId}/resource-distribution`,
      icon: <EnvironmentOutlined />,
      label: '资源分布',
    },
    {
      key: `/projects/${projectId}/race-distribution`,
      icon: <TeamOutlined />,
      label: '种族分布',
    },
    {
      key: `/projects/${projectId}/secret-realms`,
      icon: <EyeOutlined />,
      label: '秘境分布',
    },
  ] : [];

  // 设定管理菜单项
  const settingsMenuItems = projectId ? [
    {
      key: `/projects/${projectId}/world-settings`,
      icon: <GlobalOutlined />,
      label: '世界设定',
    },
    {
      key: `/projects/${projectId}/cultivation-systems`,
      icon: <ThunderboltOutlined />,
      label: '修炼体系',
    },
    {
      key: `/projects/${projectId}/equipment-systems`,
      icon: <ShoppingOutlined />,
      label: '装备体系',
    },
    {
      key: `/projects/${projectId}/pet-systems`,
      icon: <HeartOutlined />,
      label: '宠物体系',
    },
    {
      key: `/projects/${projectId}/map-structure`,
      icon: <CompassOutlined />,
      label: '地图结构',
    },
    {
      key: `/projects/${projectId}/dimension-structure`,
      icon: <StarOutlined />,
      label: '维度结构',
    },
    {
      key: `/projects/${projectId}/spiritual-treasure-systems`,
      icon: <CrownOutlined />,
      label: '灵宝体系',
    },
    {
      key: `/projects/${projectId}/civilian-systems`,
      icon: <TeamOutlined />,
      label: '生民体系',
    },
    {
      key: `/projects/${projectId}/judicial-systems`,
      icon: <BankOutlined />,
      label: '司法体系',
    },
    {
      key: `/projects/${projectId}/profession-systems`,
      icon: <UserOutlined />,
      label: '职业体系',
    },
    {
      key: `/projects/${projectId}/timeline`,
      icon: <ClockCircleOutlined />,
      label: '时间线',
    },
    {
      key: `/projects/${projectId}/relations`,
      icon: <ShareAltOutlined />,
      label: '关系网络',
    },
  ] : [];

  // 工具菜单项
  const toolsMenuItems = [
    {
      key: '/ai-assistant',
      icon: <RobotOutlined />,
      label: 'AI助手',
    },
    {
      key: '/ai-test',
      icon: <RobotOutlined />,
      label: 'AI测试',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '偏好设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  const handleCollapseMenuClick = (key) => {
    navigate(key);
  };

  const handleUserMenuClick = ({ key }) => {
    switch (key) {
      case 'profile':
        navigate('/profile');
        break;
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        // 处理退出登录
        console.log('退出登录');
        break;
      default:
        break;
    }
  };

  return (
    <AntLayout className="layout-container">
      <Header className="layout-header">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ marginRight: 16 }}
            />
            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>
              NovelCraft
            </h1>
          </div>

          <Space>
            <Button type="text" icon={<BellOutlined />} />
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <span>用户</span>
              </Space>
            </Dropdown>
          </Space>
        </div>
      </Header>

      <AntLayout className="layout-content">
        <Sider
          className="layout-sider"
          collapsed={collapsed}
          width={240}
          collapsedWidth={80}
          theme="light"
        >
          <div style={{ height: '100%', overflowY: 'auto' }}>
            {/* 基础菜单 */}
            <Menu
              mode="inline"
              selectedKeys={[location.pathname]}
              items={baseMenuItems}
              onClick={handleMenuClick}
              style={{ borderRight: 0, marginBottom: 0 }}
            />

            {/* 项目相关菜单 */}
            {projectId && (
              <>
                {/* 内容管理 */}
                <Collapse
                  ghost
                  size="small"
                  activeKey={contentCollapsed ? [] : ['content']}
                  onChange={(keys) => setContentCollapsed(!keys.includes('content'))}
                  style={{ borderRight: 0 }}
                >
                  <Panel
                    header={
                      <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
                        <BookOutlined style={{ marginRight: 8 }} />
                        <span style={{ fontWeight: 500 }}>内容管理</span>
                      </div>
                    }
                    key="content"
                    showArrow={!collapsed}
                  >
                    <Menu
                      mode="inline"
                      selectedKeys={[location.pathname]}
                      items={contentMenuItems}
                      onClick={handleMenuClick}
                      style={{ borderRight: 0, backgroundColor: 'transparent' }}
                      inlineIndent={collapsed ? 12 : 24}
                    />
                  </Panel>
                </Collapse>

                {/* 设定管理 */}
                <Collapse
                  ghost
                  size="small"
                  activeKey={settingsCollapsed ? [] : ['settings']}
                  onChange={(keys) => setSettingsCollapsed(!keys.includes('settings'))}
                  style={{ borderRight: 0 }}
                >
                  <Panel
                    header={
                      <div style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
                        <SettingOutlined style={{ marginRight: 8 }} />
                        <span style={{ fontWeight: 500 }}>设定管理</span>
                      </div>
                    }
                    key="settings"
                    showArrow={!collapsed}
                  >
                    <Menu
                      mode="inline"
                      selectedKeys={[location.pathname]}
                      items={settingsMenuItems}
                      onClick={handleMenuClick}
                      style={{ borderRight: 0, backgroundColor: 'transparent' }}
                      inlineIndent={collapsed ? 12 : 24}
                    />
                  </Panel>
                </Collapse>
              </>
            )}

            {/* 工具菜单 */}
            <div className="tools-divider">
              <div className="tools-title">
                工具
              </div>
              <Menu
                mode="inline"
                selectedKeys={[location.pathname]}
                items={toolsMenuItems}
                onClick={handleMenuClick}
                style={{ borderRight: 0 }}
              />
            </div>
          </div>
        </Sider>

        <Content className="layout-main">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
