{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\VolumeList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Progress, Tooltip, Row, Col, Statistic, Collapse, List, Divider, Tabs, Empty } from 'antd';\nimport { PlusOutlined, FileTextOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, BookOutlined, ClockCircleOutlined, CheckCircleOutlined, FolderOutlined, OrderedListOutlined, BarChartOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  Panel\n} = Collapse;\nconst VolumeList = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const navigate = useNavigate();\n  const [volumes, setVolumes] = useState([]);\n  const [chapters, setChapters] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [volumeModalVisible, setVolumeModalVisible] = useState(false);\n  const [chapterModalVisible, setChapterModalVisible] = useState(false);\n  const [editingVolume, setEditingVolume] = useState(null);\n  const [editingChapter, setEditingChapter] = useState(null);\n  const [selectedVolumeId, setSelectedVolumeId] = useState(null);\n  const [volumeForm] = Form.useForm();\n  const [chapterForm] = Form.useForm();\n\n  // 状态配置\n  const volumeStatusConfig = {\n    planning: {\n      color: 'blue',\n      text: '规划中'\n    },\n    writing: {\n      color: 'orange',\n      text: '写作中'\n    },\n    completed: {\n      color: 'green',\n      text: '已完成'\n    },\n    reviewing: {\n      color: 'purple',\n      text: '审阅中'\n    },\n    revised: {\n      color: 'cyan',\n      text: '已修订'\n    },\n    published: {\n      color: 'success',\n      text: '已发布'\n    }\n  };\n  const chapterStatusConfig = {\n    planning: {\n      color: 'blue',\n      text: '规划中'\n    },\n    draft: {\n      color: 'orange',\n      text: '草稿'\n    },\n    writing: {\n      color: 'processing',\n      text: '写作中'\n    },\n    completed: {\n      color: 'green',\n      text: '已完成'\n    },\n    published: {\n      color: 'success',\n      text: '已发布'\n    }\n  };\n\n  // 模拟卷宗数据\n  const mockVolumes = [{\n    id: 1,\n    title: '第一卷：初入修仙界',\n    volumeNumber: 1,\n    status: 'writing',\n    summary: '主角初次踏入修仙世界，遇到师父，开始修炼之路',\n    totalChapters: 10,\n    completedChapters: 6,\n    totalWords: 45000,\n    targetWords: 80000,\n    progress: 60,\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-20'\n  }, {\n    id: 2,\n    title: '第二卷：宗门试炼',\n    volumeNumber: 2,\n    status: 'planning',\n    summary: '主角参加宗门入门试炼，展现天赋，结识同门',\n    totalChapters: 8,\n    completedChapters: 0,\n    totalWords: 0,\n    targetWords: 60000,\n    progress: 0,\n    createdAt: '2024-01-21',\n    updatedAt: '2024-01-21'\n  }];\n\n  // 模拟章节数据\n  const mockChapters = [{\n    id: 1,\n    volumeId: 1,\n    title: '第一章：觉醒',\n    chapterNumber: 1,\n    content: '在这个充满灵气的世界里，少年踏上了修仙之路...',\n    wordCount: 3500,\n    status: 'published',\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-16'\n  }, {\n    id: 2,\n    volumeId: 1,\n    title: '第二章：师父',\n    chapterNumber: 2,\n    content: '经过数月的修炼，主角终于感受到了灵气的存在...',\n    wordCount: 4200,\n    status: 'completed',\n    createdAt: '2024-01-17',\n    updatedAt: '2024-01-18'\n  }, {\n    id: 3,\n    volumeId: 1,\n    title: '第三章：修炼',\n    chapterNumber: 3,\n    content: '',\n    wordCount: 0,\n    status: 'planning',\n    createdAt: '2024-01-19',\n    updatedAt: '2024-01-19'\n  }];\n  useEffect(() => {\n    setVolumes(mockVolumes);\n    setChapters(mockChapters);\n  }, []);\n\n  // 统计数据\n  const totalVolumes = volumes.length;\n  const totalChapters = volumes.reduce((sum, vol) => sum + vol.totalChapters, 0);\n  const completedChapters = volumes.reduce((sum, vol) => sum + vol.completedChapters, 0);\n  const totalWords = volumes.reduce((sum, vol) => sum + vol.totalWords, 0);\n\n  // 卷宗表格列配置\n  const volumeColumns = [{\n    title: '卷宗标题',\n    dataIndex: 'title',\n    key: 'title',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: [\"\\u7B2C\", record.volumeNumber, \"\\u5377\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '进度',\n    dataIndex: 'progress',\n    key: 'progress',\n    render: (progress, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Progress, {\n        percent: progress,\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: [record.completedChapters, \"/\", record.totalChapters, \" \\u7AE0\\u8282\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.progress - b.progress\n  }, {\n    title: '字数',\n    dataIndex: 'totalWords',\n    key: 'totalWords',\n    render: (words, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        children: [words.toLocaleString(), \" \\u5B57\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), record.targetWords && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [\"\\u76EE\\u6807: \", record.targetWords.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.totalWords - b.totalWords\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: volumeStatusConfig[status].color,\n      children: volumeStatusConfig[status].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this),\n    filters: Object.entries(volumeStatusConfig).map(([key, config]) => ({\n      text: config.text,\n      value: key\n    })),\n    onFilter: (value, record) => record.status === value\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u7AE0\\u8282\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewVolumeChapters(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\\u5377\\u5B97\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEditVolume(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u6DFB\\u52A0\\u7AE0\\u8282\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAddChapter(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u751F\\u6210\\u5927\\u7EB2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIGenerateOutline(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u5377\\u5B97\\u5417\\uFF1F\\u8FD9\\u5C06\\u540C\\u65F6\\u5220\\u9664\\u5176\\u4E0B\\u6240\\u6709\\u7AE0\\u8282\\u3002\",\n        onConfirm: () => handleDeleteVolume(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理函数\n  const handleCreateVolume = () => {\n    setEditingVolume(null);\n    volumeForm.resetFields();\n    setVolumeModalVisible(true);\n  };\n  const handleEditVolume = volume => {\n    setEditingVolume(volume);\n    volumeForm.setFieldsValue(volume);\n    setVolumeModalVisible(true);\n  };\n  const handleViewVolumeChapters = volume => {\n    setSelectedVolumeId(volume.id);\n    message.info(`查看卷宗《${volume.title}》的章节`);\n  };\n  const handleAddChapter = volume => {\n    setSelectedVolumeId(volume.id);\n    setEditingChapter(null);\n    chapterForm.resetFields();\n    chapterForm.setFieldsValue({\n      volumeId: volume.id\n    });\n    setChapterModalVisible(true);\n  };\n  const handleAIGenerateOutline = volume => {\n    message.info(`AI生成卷宗《${volume.title}》大纲功能`);\n  };\n  const handleDeleteVolume = id => {\n    setVolumes(volumes.filter(v => v.id !== id));\n    setChapters(chapters.filter(c => c.volumeId !== id));\n    message.success('卷宗删除成功');\n  };\n  const handleVolumeModalOk = async () => {\n    try {\n      const values = await volumeForm.validateFields();\n      setLoading(true);\n      if (editingVolume) {\n        // 编辑卷宗\n        setVolumes(volumes.map(v => v.id === editingVolume.id ? {\n          ...v,\n          ...values,\n          updatedAt: new Date().toISOString().split('T')[0]\n        } : v));\n        message.success('卷宗更新成功');\n      } else {\n        // 新建卷宗\n        const newVolume = {\n          id: Date.now(),\n          ...values,\n          totalChapters: 0,\n          completedChapters: 0,\n          totalWords: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setVolumes([...volumes, newVolume]);\n        message.success('卷宗创建成功');\n      }\n      setVolumeModalVisible(false);\n      volumeForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChapterModalOk = async () => {\n    try {\n      const values = await chapterForm.validateFields();\n      setLoading(true);\n      const newChapter = {\n        id: Date.now(),\n        ...values,\n        wordCount: 0,\n        createdAt: new Date().toISOString().split('T')[0],\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n      setChapters([...chapters, newChapter]);\n      message.success('章节创建成功');\n      setChapterModalVisible(false);\n      chapterForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取指定卷宗的章节\n  const getVolumeChapters = volumeId => {\n    return chapters.filter(c => c.volumeId === volumeId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u5377\\u5B97\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5377\\u5B97\\u6570\",\n            value: totalVolumes,\n            prefix: /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u7AE0\\u8282\\u6570\",\n            value: totalChapters,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u7AE0\\u8282\",\n            value: completedChapters,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5B57\\u6570\",\n            value: totalWords,\n            suffix: \"\\u5B57\",\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreateVolume,\n            children: \"\\u65B0\\u5EFA\\u5377\\u5B97\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: volumeColumns,\n        dataSource: volumes,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个卷宗`\n        },\n        expandable: {\n          expandedRowRender: volume => {\n            const volumeChapters = getVolumeChapters(volume.id);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                margin: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(Divider, {\n                orientation: \"left\",\n                children: \"\\u7AE0\\u8282\\u5217\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                size: \"small\",\n                dataSource: volumeChapters,\n                renderItem: chapter => /*#__PURE__*/_jsxDEV(List.Item, {\n                  actions: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    size: \"small\",\n                    onClick: () => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`),\n                    children: \"\\u7F16\\u8F91\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    size: \"small\",\n                    onClick: () => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`),\n                    children: \"\\u67E5\\u770B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 27\n                  }, this)],\n                  children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                    avatar: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 35\n                    }, this),\n                    title: /*#__PURE__*/_jsxDEV(Space, {\n                      children: [chapter.title, /*#__PURE__*/_jsxDEV(Tag, {\n                        color: chapterStatusConfig[chapter.status].color,\n                        children: chapterStatusConfig[chapter.status].text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 482,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 29\n                    }, this),\n                    description: `字数: ${chapter.wordCount} | 更新: ${chapter.updatedAt}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this), volumeChapters.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  padding: '20px',\n                  color: '#999'\n                },\n                children: \"\\u6682\\u65E0\\u7AE0\\u8282\\uFF0C\\u70B9\\u51FB\\u4E0A\\u65B9\\\"\\u6DFB\\u52A0\\u7AE0\\u8282\\\"\\u6309\\u94AE\\u521B\\u5EFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this);\n          },\n          rowExpandable: () => true\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingVolume ? '编辑卷宗' : '新建卷宗',\n      open: volumeModalVisible,\n      onOk: handleVolumeModalOk,\n      onCancel: () => {\n        setVolumeModalVisible(false);\n        volumeForm.resetFields();\n      },\n      confirmLoading: loading,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: volumeForm,\n        layout: \"vertical\",\n        initialValues: {\n          status: 'planning'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u5377\\u5B97\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入卷宗标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5377\\u5B97\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"volumeNumber\",\n              label: \"\\u5377\\u5E8F\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入卷序号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"summary\",\n          label: \"\\u5377\\u5B97\\u6458\\u8981\",\n          rules: [{\n            required: true,\n            message: '请输入卷宗摘要'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u7B80\\u8981\\u63CF\\u8FF0\\u672C\\u5377\\u7684\\u4E3B\\u8981\\u5185\\u5BB9\\u548C\\u5267\\u60C5\\u53D1\\u5C55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择卷宗状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: Object.entries(volumeStatusConfig).map(([key, config]) => /*#__PURE__*/_jsxDEV(Option, {\n                  value: key,\n                  children: config.text\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"targetWords\",\n              label: \"\\u76EE\\u6807\\u5B57\\u6570\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"80000\",\n                suffix: \"\\u5B57\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"outline\",\n          label: \"\\u5377\\u5B97\\u5927\\u7EB2\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 6,\n            placeholder: \"\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u672C\\u5377\\u7684\\u7AE0\\u8282\\u5B89\\u6392\\u548C\\u5267\\u60C5\\u53D1\\u5C55...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u65B0\\u5EFA\\u7AE0\\u8282\",\n      open: chapterModalVisible,\n      onOk: handleChapterModalOk,\n      onCancel: () => {\n        setChapterModalVisible(false);\n        chapterForm.resetFields();\n      },\n      confirmLoading: loading,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: chapterForm,\n        layout: \"vertical\",\n        initialValues: {\n          status: 'planning'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"volumeId\",\n          label: \"\\u6240\\u5C5E\\u5377\\u5B97\",\n          rules: [{\n            required: true,\n            message: '请选择所属卷宗'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5377\\u5B97\",\n            children: volumes.map(volume => /*#__PURE__*/_jsxDEV(Option, {\n              value: volume.id,\n              children: volume.title\n            }, volume.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u7AE0\\u8282\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入章节标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7AE0\\u8282\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"chapterNumber\",\n              label: \"\\u7AE0\\u8282\\u5E8F\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入章节序号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择章节状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: Object.entries(chapterStatusConfig).map(([key, config]) => /*#__PURE__*/_jsxDEV(Option, {\n              value: key,\n              children: config.text\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"outline\",\n          label: \"\\u7AE0\\u8282\\u5927\\u7EB2\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u7B80\\u8981\\u63CF\\u8FF0\\u672C\\u7AE0\\u8282\\u7684\\u4E3B\\u8981\\u5185\\u5BB9\\u548C\\u60C5\\u8282\\u53D1\\u5C55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 380,\n    columnNumber: 5\n  }, this);\n};\n_s(VolumeList, \"XTiLBe4g2feh3UCrXirmJvfFc5Y=\", false, function () {\n  return [useParams, useNavigate, Form.useForm, Form.useForm];\n});\n_c = VolumeList;\nexport default VolumeList;\nvar _c;\n$RefreshReg$(_c, \"VolumeList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Progress", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Collapse", "List", "Divider", "Tabs", "Empty", "PlusOutlined", "FileTextOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "BookOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "FolderOutlined", "OrderedListOutlined", "BarChartOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "Panel", "VolumeList", "_s", "id", "projectId", "navigate", "volumes", "setVolumes", "chapters", "setChapters", "loading", "setLoading", "volumeModalVisible", "setVolumeModalVisible", "chapterModalVisible", "setChapterModalVisible", "editingVolume", "setEditingVolume", "editing<PERSON><PERSON>pter", "setEditingChapter", "selectedVolumeId", "setSelectedVolumeId", "volumeForm", "useForm", "chapterForm", "volumeStatusConfig", "planning", "color", "text", "writing", "completed", "reviewing", "revised", "published", "chapterStatusConfig", "draft", "mockVolumes", "title", "volumeNumber", "status", "summary", "totalChapters", "completedChapters", "totalWords", "targetWords", "progress", "createdAt", "updatedAt", "mockChapters", "volumeId", "chapterNumber", "content", "wordCount", "totalVolumes", "length", "reduce", "sum", "vol", "volumeColumns", "dataIndex", "key", "render", "record", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strong", "percent", "size", "type", "sorter", "a", "b", "words", "toLocaleString", "filters", "Object", "entries", "map", "config", "value", "onFilter", "Date", "_", "icon", "onClick", "handleViewVolumeChapters", "handleEditVolume", "handleAddChapter", "handleAIGenerateOutline", "onConfirm", "handleDeleteVolume", "okText", "cancelText", "danger", "handleCreateVolume", "resetFields", "volume", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "filter", "v", "c", "success", "handleVolumeModalOk", "values", "validateFields", "toISOString", "split", "newVolume", "now", "error", "console", "handleChapterModalOk", "newChapter", "getVolumeChapters", "className", "level", "gutter", "style", "marginBottom", "span", "prefix", "suffix", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "expandable", "expandedRowRender", "volumeChapters", "margin", "orientation", "renderItem", "chapter", "<PERSON><PERSON>", "actions", "Meta", "avatar", "description", "textAlign", "padding", "rowExpandable", "open", "onOk", "onCancel", "confirmLoading", "width", "form", "layout", "initialValues", "name", "label", "rules", "required", "placeholder", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/VolumeList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Progress,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Collapse,\n  List,\n  Divider,\n  Tabs,\n  Empty\n} from 'antd';\nimport {\n  PlusOutlined,\n  FileTextOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  BookOutlined,\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  FolderOutlined,\n  OrderedListOutlined,\n  BarChartOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { Panel } = Collapse;\n\nconst VolumeList = () => {\n  const { id: projectId } = useParams();\n  const navigate = useNavigate();\n  const [volumes, setVolumes] = useState([]);\n  const [chapters, setChapters] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [volumeModalVisible, setVolumeModalVisible] = useState(false);\n  const [chapterModalVisible, setChapterModalVisible] = useState(false);\n  const [editingVolume, setEditingVolume] = useState(null);\n  const [editingChapter, setEditingChapter] = useState(null);\n  const [selectedVolumeId, setSelectedVolumeId] = useState(null);\n  const [volumeForm] = Form.useForm();\n  const [chapterForm] = Form.useForm();\n\n  // 状态配置\n  const volumeStatusConfig = {\n    planning: { color: 'blue', text: '规划中' },\n    writing: { color: 'orange', text: '写作中' },\n    completed: { color: 'green', text: '已完成' },\n    reviewing: { color: 'purple', text: '审阅中' },\n    revised: { color: 'cyan', text: '已修订' },\n    published: { color: 'success', text: '已发布' }\n  };\n\n  const chapterStatusConfig = {\n    planning: { color: 'blue', text: '规划中' },\n    draft: { color: 'orange', text: '草稿' },\n    writing: { color: 'processing', text: '写作中' },\n    completed: { color: 'green', text: '已完成' },\n    published: { color: 'success', text: '已发布' }\n  };\n\n  // 模拟卷宗数据\n  const mockVolumes = [\n    {\n      id: 1,\n      title: '第一卷：初入修仙界',\n      volumeNumber: 1,\n      status: 'writing',\n      summary: '主角初次踏入修仙世界，遇到师父，开始修炼之路',\n      totalChapters: 10,\n      completedChapters: 6,\n      totalWords: 45000,\n      targetWords: 80000,\n      progress: 60,\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-20'\n    },\n    {\n      id: 2,\n      title: '第二卷：宗门试炼',\n      volumeNumber: 2,\n      status: 'planning',\n      summary: '主角参加宗门入门试炼，展现天赋，结识同门',\n      totalChapters: 8,\n      completedChapters: 0,\n      totalWords: 0,\n      targetWords: 60000,\n      progress: 0,\n      createdAt: '2024-01-21',\n      updatedAt: '2024-01-21'\n    }\n  ];\n\n  // 模拟章节数据\n  const mockChapters = [\n    {\n      id: 1,\n      volumeId: 1,\n      title: '第一章：觉醒',\n      chapterNumber: 1,\n      content: '在这个充满灵气的世界里，少年踏上了修仙之路...',\n      wordCount: 3500,\n      status: 'published',\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-16'\n    },\n    {\n      id: 2,\n      volumeId: 1,\n      title: '第二章：师父',\n      chapterNumber: 2,\n      content: '经过数月的修炼，主角终于感受到了灵气的存在...',\n      wordCount: 4200,\n      status: 'completed',\n      createdAt: '2024-01-17',\n      updatedAt: '2024-01-18'\n    },\n    {\n      id: 3,\n      volumeId: 1,\n      title: '第三章：修炼',\n      chapterNumber: 3,\n      content: '',\n      wordCount: 0,\n      status: 'planning',\n      createdAt: '2024-01-19',\n      updatedAt: '2024-01-19'\n    }\n  ];\n\n  useEffect(() => {\n    setVolumes(mockVolumes);\n    setChapters(mockChapters);\n  }, []);\n\n  // 统计数据\n  const totalVolumes = volumes.length;\n  const totalChapters = volumes.reduce((sum, vol) => sum + vol.totalChapters, 0);\n  const completedChapters = volumes.reduce((sum, vol) => sum + vol.completedChapters, 0);\n  const totalWords = volumes.reduce((sum, vol) => sum + vol.totalWords, 0);\n\n  // 卷宗表格列配置\n  const volumeColumns = [\n    {\n      title: '卷宗标题',\n      dataIndex: 'title',\n      key: 'title',\n      render: (text, record) => (\n        <Space>\n          <FolderOutlined />\n          <Text strong>{text}</Text>\n          <Tag color=\"blue\">第{record.volumeNumber}卷</Tag>\n        </Space>\n      )\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      render: (progress, record) => (\n        <div>\n          <Progress percent={progress} size=\"small\" />\n          <Text type=\"secondary\">\n            {record.completedChapters}/{record.totalChapters} 章节\n          </Text>\n        </div>\n      ),\n      sorter: (a, b) => a.progress - b.progress\n    },\n    {\n      title: '字数',\n      dataIndex: 'totalWords',\n      key: 'totalWords',\n      render: (words, record) => (\n        <div>\n          <Text>{words.toLocaleString()} 字</Text>\n          {record.targetWords && (\n            <div>\n              <Text type=\"secondary\">目标: {record.targetWords.toLocaleString()}</Text>\n            </div>\n          )}\n        </div>\n      ),\n      sorter: (a, b) => a.totalWords - b.totalWords\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={volumeStatusConfig[status].color}>\n          {volumeStatusConfig[status].text}\n        </Tag>\n      ),\n      filters: Object.entries(volumeStatusConfig).map(([key, config]) => ({\n        text: config.text,\n        value: key\n      })),\n      onFilter: (value, record) => record.status === value\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看章节\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewVolumeChapters(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑卷宗\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditVolume(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"添加章节\">\n            <Button\n              type=\"text\"\n              icon={<PlusOutlined />}\n              onClick={() => handleAddChapter(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI生成大纲\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIGenerateOutline(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个卷宗吗？这将同时删除其下所有章节。\"\n            onConfirm={() => handleDeleteVolume(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 处理函数\n  const handleCreateVolume = () => {\n    setEditingVolume(null);\n    volumeForm.resetFields();\n    setVolumeModalVisible(true);\n  };\n\n  const handleEditVolume = (volume) => {\n    setEditingVolume(volume);\n    volumeForm.setFieldsValue(volume);\n    setVolumeModalVisible(true);\n  };\n\n  const handleViewVolumeChapters = (volume) => {\n    setSelectedVolumeId(volume.id);\n    message.info(`查看卷宗《${volume.title}》的章节`);\n  };\n\n  const handleAddChapter = (volume) => {\n    setSelectedVolumeId(volume.id);\n    setEditingChapter(null);\n    chapterForm.resetFields();\n    chapterForm.setFieldsValue({ volumeId: volume.id });\n    setChapterModalVisible(true);\n  };\n\n  const handleAIGenerateOutline = (volume) => {\n    message.info(`AI生成卷宗《${volume.title}》大纲功能`);\n  };\n\n  const handleDeleteVolume = (id) => {\n    setVolumes(volumes.filter(v => v.id !== id));\n    setChapters(chapters.filter(c => c.volumeId !== id));\n    message.success('卷宗删除成功');\n  };\n\n  const handleVolumeModalOk = async () => {\n    try {\n      const values = await volumeForm.validateFields();\n      setLoading(true);\n\n      if (editingVolume) {\n        // 编辑卷宗\n        setVolumes(volumes.map(v =>\n          v.id === editingVolume.id\n            ? { ...v, ...values, updatedAt: new Date().toISOString().split('T')[0] }\n            : v\n        ));\n        message.success('卷宗更新成功');\n      } else {\n        // 新建卷宗\n        const newVolume = {\n          id: Date.now(),\n          ...values,\n          totalChapters: 0,\n          completedChapters: 0,\n          totalWords: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setVolumes([...volumes, newVolume]);\n        message.success('卷宗创建成功');\n      }\n\n      setVolumeModalVisible(false);\n      volumeForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChapterModalOk = async () => {\n    try {\n      const values = await chapterForm.validateFields();\n      setLoading(true);\n\n      const newChapter = {\n        id: Date.now(),\n        ...values,\n        wordCount: 0,\n        createdAt: new Date().toISOString().split('T')[0],\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n      setChapters([...chapters, newChapter]);\n      message.success('章节创建成功');\n\n      setChapterModalVisible(false);\n      chapterForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取指定卷宗的章节\n  const getVolumeChapters = (volumeId) => {\n    return chapters.filter(c => c.volumeId === volumeId);\n  };\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">卷宗管理</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总卷宗数\"\n              value={totalVolumes}\n              prefix={<FolderOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总章节数\"\n              value={totalChapters}\n              prefix={<BookOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已完成章节\"\n              value={completedChapters}\n              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总字数\"\n              value={totalWords}\n              suffix=\"字\"\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateVolume}\n            >\n              新建卷宗\n            </Button>\n          </div>\n        </div>\n\n        <Table\n          columns={volumeColumns}\n          dataSource={volumes}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个卷宗`\n          }}\n          expandable={{\n            expandedRowRender: (volume) => {\n              const volumeChapters = getVolumeChapters(volume.id);\n              return (\n                <div style={{ margin: 0 }}>\n                  <Divider orientation=\"left\">章节列表</Divider>\n                  <List\n                    size=\"small\"\n                    dataSource={volumeChapters}\n                    renderItem={(chapter) => (\n                      <List.Item\n                        actions={[\n                          <Button\n                            type=\"link\"\n                            size=\"small\"\n                            onClick={() => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`)}\n                          >\n                            编辑\n                          </Button>,\n                          <Button\n                            type=\"link\"\n                            size=\"small\"\n                            onClick={() => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`)}\n                          >\n                            查看\n                          </Button>\n                        ]}\n                      >\n                        <List.Item.Meta\n                          avatar={<FileTextOutlined />}\n                          title={\n                            <Space>\n                              {chapter.title}\n                              <Tag color={chapterStatusConfig[chapter.status].color}>\n                                {chapterStatusConfig[chapter.status].text}\n                              </Tag>\n                            </Space>\n                          }\n                          description={`字数: ${chapter.wordCount} | 更新: ${chapter.updatedAt}`}\n                        />\n                      </List.Item>\n                    )}\n                  />\n                  {volumeChapters.length === 0 && (\n                    <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>\n                      暂无章节，点击上方\"添加章节\"按钮创建\n                    </div>\n                  )}\n                </div>\n              );\n            },\n            rowExpandable: () => true,\n          }}\n        />\n      </Card>\n\n      {/* 新建/编辑卷宗模态框 */}\n      <Modal\n        title={editingVolume ? '编辑卷宗' : '新建卷宗'}\n        open={volumeModalVisible}\n        onOk={handleVolumeModalOk}\n        onCancel={() => {\n          setVolumeModalVisible(false);\n          volumeForm.resetFields();\n        }}\n        confirmLoading={loading}\n        width={800}\n      >\n        <Form\n          form={volumeForm}\n          layout=\"vertical\"\n          initialValues={{ status: 'planning' }}\n        >\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"卷宗标题\"\n                rules={[{ required: true, message: '请输入卷宗标题' }]}\n              >\n                <Input placeholder=\"请输入卷宗标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"volumeNumber\"\n                label=\"卷序号\"\n                rules={[{ required: true, message: '请输入卷序号' }]}\n              >\n                <Input type=\"number\" placeholder=\"1\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"summary\"\n            label=\"卷宗摘要\"\n            rules={[{ required: true, message: '请输入卷宗摘要' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请简要描述本卷的主要内容和剧情发展\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择卷宗状态' }]}\n              >\n                <Select>\n                  {Object.entries(volumeStatusConfig).map(([key, config]) => (\n                    <Option key={key} value={key}>{config.text}</Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"targetWords\"\n                label=\"目标字数\"\n              >\n                <Input type=\"number\" placeholder=\"80000\" suffix=\"字\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"outline\"\n            label=\"卷宗大纲\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"详细描述本卷的章节安排和剧情发展...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 新建章节模态框 */}\n      <Modal\n        title=\"新建章节\"\n        open={chapterModalVisible}\n        onOk={handleChapterModalOk}\n        onCancel={() => {\n          setChapterModalVisible(false);\n          chapterForm.resetFields();\n        }}\n        confirmLoading={loading}\n        width={600}\n      >\n        <Form\n          form={chapterForm}\n          layout=\"vertical\"\n          initialValues={{ status: 'planning' }}\n        >\n          <Form.Item\n            name=\"volumeId\"\n            label=\"所属卷宗\"\n            rules={[{ required: true, message: '请选择所属卷宗' }]}\n          >\n            <Select placeholder=\"请选择卷宗\">\n              {volumes.map(volume => (\n                <Option key={volume.id} value={volume.id}>\n                  {volume.title}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"章节标题\"\n                rules={[{ required: true, message: '请输入章节标题' }]}\n              >\n                <Input placeholder=\"请输入章节标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"chapterNumber\"\n                label=\"章节序号\"\n                rules={[{ required: true, message: '请输入章节序号' }]}\n              >\n                <Input type=\"number\" placeholder=\"1\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            rules={[{ required: true, message: '请选择章节状态' }]}\n          >\n            <Select>\n              {Object.entries(chapterStatusConfig).map(([key, config]) => (\n                <Option key={key} value={key}>{config.text}</Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"outline\"\n            label=\"章节大纲\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请简要描述本章节的主要内容和情节发展\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default VolumeList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,mBAAmB,EACnBC,gBAAgB,QACX,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpC,UAAU;AAClC,MAAM;EAAEqC;AAAS,CAAC,GAAG9B,KAAK;AAC1B,MAAM;EAAE+B;AAAO,CAAC,GAAG9B,MAAM;AACzB,MAAM;EAAE+B;AAAM,CAAC,GAAGvB,QAAQ;AAE1B,MAAMwB,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAG9C,SAAS,CAAC,CAAC;EACrC,MAAM+C,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkE,UAAU,CAAC,GAAGvD,IAAI,CAACwD,OAAO,CAAC,CAAC;EACnC,MAAM,CAACC,WAAW,CAAC,GAAGzD,IAAI,CAACwD,OAAO,CAAC,CAAC;;EAEpC;EACA,MAAME,kBAAkB,GAAG;IACzBC,QAAQ,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACxCC,OAAO,EAAE;MAAEF,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAC;IACzCE,SAAS,EAAE;MAAEH,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1CG,SAAS,EAAE;MAAEJ,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC3CI,OAAO,EAAE;MAAEL,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACvCK,SAAS,EAAE;MAAEN,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM;EAC7C,CAAC;EAED,MAAMM,mBAAmB,GAAG;IAC1BR,QAAQ,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACxCO,KAAK,EAAE;MAAER,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAC;IACtCC,OAAO,EAAE;MAAEF,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC7CE,SAAS,EAAE;MAAEH,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1CK,SAAS,EAAE;MAAEN,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM;EAC7C,CAAC;;EAED;EACA,MAAMQ,WAAW,GAAG,CAClB;IACEjC,EAAE,EAAE,CAAC;IACLkC,KAAK,EAAE,WAAW;IAClBC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,wBAAwB;IACjCC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,CAAC;IACpBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE5C,EAAE,EAAE,CAAC;IACLkC,KAAK,EAAE,UAAU;IACjBC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE,sBAAsB;IAC/BC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE,CAAC;IACpBC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IACE7C,EAAE,EAAE,CAAC;IACL8C,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,IAAI;IACfb,MAAM,EAAE,WAAW;IACnBO,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE5C,EAAE,EAAE,CAAC;IACL8C,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,IAAI;IACfb,MAAM,EAAE,WAAW;IACnBO,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE5C,EAAE,EAAE,CAAC;IACL8C,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,CAAC;IACZb,MAAM,EAAE,UAAU;IAClBO,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EAED1F,SAAS,CAAC,MAAM;IACdkD,UAAU,CAAC6B,WAAW,CAAC;IACvB3B,WAAW,CAACuC,YAAY,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,YAAY,GAAG/C,OAAO,CAACgD,MAAM;EACnC,MAAMb,aAAa,GAAGnC,OAAO,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAAChB,aAAa,EAAE,CAAC,CAAC;EAC9E,MAAMC,iBAAiB,GAAGpC,OAAO,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACf,iBAAiB,EAAE,CAAC,CAAC;EACtF,MAAMC,UAAU,GAAGrC,OAAO,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACd,UAAU,EAAE,CAAC,CAAC;;EAExE;EACA,MAAMe,aAAa,GAAG,CACpB;IACErB,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACjC,IAAI,EAAEkC,MAAM,kBACnBnE,OAAA,CAAC/B,KAAK;MAAAmG,QAAA,gBACJpE,OAAA,CAACJ,cAAc;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClBxE,OAAA,CAACE,IAAI;QAACuE,MAAM;QAAAL,QAAA,EAAEnC;MAAI;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BxE,OAAA,CAAC9B,GAAG;QAAC8D,KAAK,EAAC,MAAM;QAAAoC,QAAA,GAAC,QAAC,EAACD,MAAM,CAACxB,YAAY,EAAC,QAAC;MAAA;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C;EAEX,CAAC,EACD;IACE9B,KAAK,EAAE,IAAI;IACXsB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAEA,CAAChB,QAAQ,EAAEiB,MAAM,kBACvBnE,OAAA;MAAAoE,QAAA,gBACEpE,OAAA,CAACvB,QAAQ;QAACiG,OAAO,EAAExB,QAAS;QAACyB,IAAI,EAAC;MAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5CxE,OAAA,CAACE,IAAI;QAAC0E,IAAI,EAAC,WAAW;QAAAR,QAAA,GACnBD,MAAM,CAACpB,iBAAiB,EAAC,GAAC,EAACoB,MAAM,CAACrB,aAAa,EAAC,eACnD;MAAA;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;IACDK,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5B,QAAQ,GAAG6B,CAAC,CAAC7B;EACnC,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACXsB,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACc,KAAK,EAAEb,MAAM,kBACpBnE,OAAA;MAAAoE,QAAA,gBACEpE,OAAA,CAACE,IAAI;QAAAkE,QAAA,GAAEY,KAAK,CAACC,cAAc,CAAC,CAAC,EAAC,SAAE;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACtCL,MAAM,CAAClB,WAAW,iBACjBjD,OAAA;QAAAoE,QAAA,eACEpE,OAAA,CAACE,IAAI;UAAC0E,IAAI,EAAC,WAAW;UAAAR,QAAA,GAAC,gBAAI,EAACD,MAAM,CAAClB,WAAW,CAACgC,cAAc,CAAC,CAAC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;IACDK,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC9B,UAAU,GAAG+B,CAAC,CAAC/B;EACrC,CAAC,EACD;IACEN,KAAK,EAAE,IAAI;IACXsB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGtB,MAAM,iBACb5C,OAAA,CAAC9B,GAAG;MAAC8D,KAAK,EAAEF,kBAAkB,CAACc,MAAM,CAAC,CAACZ,KAAM;MAAAoC,QAAA,EAC1CtC,kBAAkB,CAACc,MAAM,CAAC,CAACX;IAAI;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACN;IACDU,OAAO,EAAEC,MAAM,CAACC,OAAO,CAACtD,kBAAkB,CAAC,CAACuD,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,MAAM;MAClErD,IAAI,EAAEqD,MAAM,CAACrD,IAAI;MACjBsD,KAAK,EAAEtB;IACT,CAAC,CAAC,CAAC;IACHuB,QAAQ,EAAEA,CAACD,KAAK,EAAEpB,MAAM,KAAKA,MAAM,CAACvB,MAAM,KAAK2C;EACjD,CAAC,EACD;IACE7C,KAAK,EAAE,MAAM;IACbsB,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBY,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIU,IAAI,CAACX,CAAC,CAAC1B,SAAS,CAAC,GAAG,IAAIqC,IAAI,CAACV,CAAC,CAAC3B,SAAS;EAChE,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXuB,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACwB,CAAC,EAAEvB,MAAM,kBAChBnE,OAAA,CAAC/B,KAAK;MAAAmG,QAAA,gBACJpE,OAAA,CAACtB,OAAO;QAACgE,KAAK,EAAC,0BAAM;QAAA0B,QAAA,eACnBpE,OAAA,CAACjC,MAAM;UACL6G,IAAI,EAAC,MAAM;UACXe,IAAI,eAAE3F,OAAA,CAACT,WAAW;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBoB,OAAO,EAAEA,CAAA,KAAMC,wBAAwB,CAAC1B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxE,OAAA,CAACtB,OAAO;QAACgE,KAAK,EAAC,0BAAM;QAAA0B,QAAA,eACnBpE,OAAA,CAACjC,MAAM;UACL6G,IAAI,EAAC,MAAM;UACXe,IAAI,eAAE3F,OAAA,CAACX,YAAY;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoB,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAAC3B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxE,OAAA,CAACtB,OAAO;QAACgE,KAAK,EAAC,0BAAM;QAAA0B,QAAA,eACnBpE,OAAA,CAACjC,MAAM;UACL6G,IAAI,EAAC,MAAM;UACXe,IAAI,eAAE3F,OAAA,CAACb,YAAY;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoB,OAAO,EAAEA,CAAA,KAAMG,gBAAgB,CAAC5B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxE,OAAA,CAACtB,OAAO;QAACgE,KAAK,EAAC,4BAAQ;QAAA0B,QAAA,eACrBpE,OAAA,CAACjC,MAAM;UACL6G,IAAI,EAAC,MAAM;UACXe,IAAI,eAAE3F,OAAA,CAACR,aAAa;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBoB,OAAO,EAAEA,CAAA,KAAMI,uBAAuB,CAAC7B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxE,OAAA,CAACxB,UAAU;QACTkE,KAAK,EAAC,4IAAyB;QAC/BuD,SAAS,EAAEA,CAAA,KAAMC,kBAAkB,CAAC/B,MAAM,CAAC3D,EAAE,CAAE;QAC/C2F,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAhC,QAAA,eAEfpE,OAAA,CAACtB,OAAO;UAACgE,KAAK,EAAC,cAAI;UAAA0B,QAAA,eACjBpE,OAAA,CAACjC,MAAM;YACL6G,IAAI,EAAC,MAAM;YACXyB,MAAM;YACNV,IAAI,eAAE3F,OAAA,CAACV,cAAc;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAM8B,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhF,gBAAgB,CAAC,IAAI,CAAC;IACtBK,UAAU,CAAC4E,WAAW,CAAC,CAAC;IACxBrF,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM4E,gBAAgB,GAAIU,MAAM,IAAK;IACnClF,gBAAgB,CAACkF,MAAM,CAAC;IACxB7E,UAAU,CAAC8E,cAAc,CAACD,MAAM,CAAC;IACjCtF,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM2E,wBAAwB,GAAIW,MAAM,IAAK;IAC3C9E,mBAAmB,CAAC8E,MAAM,CAAChG,EAAE,CAAC;IAC9BjC,OAAO,CAACmI,IAAI,CAAC,QAAQF,MAAM,CAAC9D,KAAK,MAAM,CAAC;EAC1C,CAAC;EAED,MAAMqD,gBAAgB,GAAIS,MAAM,IAAK;IACnC9E,mBAAmB,CAAC8E,MAAM,CAAChG,EAAE,CAAC;IAC9BgB,iBAAiB,CAAC,IAAI,CAAC;IACvBK,WAAW,CAAC0E,WAAW,CAAC,CAAC;IACzB1E,WAAW,CAAC4E,cAAc,CAAC;MAAEnD,QAAQ,EAAEkD,MAAM,CAAChG;IAAG,CAAC,CAAC;IACnDY,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM4E,uBAAuB,GAAIQ,MAAM,IAAK;IAC1CjI,OAAO,CAACmI,IAAI,CAAC,UAAUF,MAAM,CAAC9D,KAAK,OAAO,CAAC;EAC7C,CAAC;EAED,MAAMwD,kBAAkB,GAAI1F,EAAE,IAAK;IACjCI,UAAU,CAACD,OAAO,CAACgG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpG,EAAE,KAAKA,EAAE,CAAC,CAAC;IAC5CM,WAAW,CAACD,QAAQ,CAAC8F,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACvD,QAAQ,KAAK9C,EAAE,CAAC,CAAC;IACpDjC,OAAO,CAACuI,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMrF,UAAU,CAACsF,cAAc,CAAC,CAAC;MAChDjG,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,aAAa,EAAE;QACjB;QACAT,UAAU,CAACD,OAAO,CAAC0E,GAAG,CAACuB,CAAC,IACtBA,CAAC,CAACpG,EAAE,KAAKa,aAAa,CAACb,EAAE,GACrB;UAAE,GAAGoG,CAAC;UAAE,GAAGI,MAAM;UAAE5D,SAAS,EAAE,IAAIqC,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC,GACtEP,CACN,CAAC,CAAC;QACFrI,OAAO,CAACuI,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMM,SAAS,GAAG;UAChB5G,EAAE,EAAEiF,IAAI,CAAC4B,GAAG,CAAC,CAAC;UACd,GAAGL,MAAM;UACTlE,aAAa,EAAE,CAAC;UAChBC,iBAAiB,EAAE,CAAC;UACpBC,UAAU,EAAE,CAAC;UACbE,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjD/D,SAAS,EAAE,IAAIqC,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACDvG,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAEyG,SAAS,CAAC,CAAC;QACnC7I,OAAO,CAACuI,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEA5F,qBAAqB,CAAC,KAAK,CAAC;MAC5BS,UAAU,CAAC4E,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRtG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwG,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMR,MAAM,GAAG,MAAMnF,WAAW,CAACoF,cAAc,CAAC,CAAC;MACjDjG,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMyG,UAAU,GAAG;QACjBjH,EAAE,EAAEiF,IAAI,CAAC4B,GAAG,CAAC,CAAC;QACd,GAAGL,MAAM;QACTvD,SAAS,EAAE,CAAC;QACZN,SAAS,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjD/D,SAAS,EAAE,IAAIqC,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAClD,CAAC;MACDrG,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE4G,UAAU,CAAC,CAAC;MACtClJ,OAAO,CAACuI,OAAO,CAAC,QAAQ,CAAC;MAEzB1F,sBAAsB,CAAC,KAAK,CAAC;MAC7BS,WAAW,CAAC0E,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRtG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0G,iBAAiB,GAAIpE,QAAQ,IAAK;IACtC,OAAOzC,QAAQ,CAAC8F,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACvD,QAAQ,KAAKA,QAAQ,CAAC;EACtD,CAAC;EAED,oBACEtD,OAAA;IAAK2H,SAAS,EAAC,SAAS;IAAAvD,QAAA,gBACtBpE,OAAA;MAAK2H,SAAS,EAAC,aAAa;MAAAvD,QAAA,eAC1BpE,OAAA,CAACC,KAAK;QAAC2H,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAAvD,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNxE,OAAA,CAACrB,GAAG;MAACkJ,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAA3D,QAAA,gBAC3CpE,OAAA,CAACpB,GAAG;QAACoJ,IAAI,EAAE,CAAE;QAAA5D,QAAA,eACXpE,OAAA,CAACnC,IAAI;UAAAuG,QAAA,eACHpE,OAAA,CAACnB,SAAS;YACR6D,KAAK,EAAC,0BAAM;YACZ6C,KAAK,EAAE7B,YAAa;YACpBuE,MAAM,eAAEjI,OAAA,CAACJ,cAAc;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxE,OAAA,CAACpB,GAAG;QAACoJ,IAAI,EAAE,CAAE;QAAA5D,QAAA,eACXpE,OAAA,CAACnC,IAAI;UAAAuG,QAAA,eACHpE,OAAA,CAACnB,SAAS;YACR6D,KAAK,EAAC,0BAAM;YACZ6C,KAAK,EAAEzC,aAAc;YACrBmF,MAAM,eAAEjI,OAAA,CAACP,YAAY;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxE,OAAA,CAACpB,GAAG;QAACoJ,IAAI,EAAE,CAAE;QAAA5D,QAAA,eACXpE,OAAA,CAACnC,IAAI;UAAAuG,QAAA,eACHpE,OAAA,CAACnB,SAAS;YACR6D,KAAK,EAAC,gCAAO;YACb6C,KAAK,EAAExC,iBAAkB;YACzBkF,MAAM,eAAEjI,OAAA,CAACL,mBAAmB;cAACmI,KAAK,EAAE;gBAAE9F,KAAK,EAAE;cAAU;YAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxE,OAAA,CAACpB,GAAG;QAACoJ,IAAI,EAAE,CAAE;QAAA5D,QAAA,eACXpE,OAAA,CAACnC,IAAI;UAAAuG,QAAA,eACHpE,OAAA,CAACnB,SAAS;YACR6D,KAAK,EAAC,oBAAK;YACX6C,KAAK,EAAEvC,UAAW;YAClBkF,MAAM,EAAC,QAAG;YACVD,MAAM,eAAEjI,OAAA,CAACZ,gBAAgB;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxE,OAAA,CAACnC,IAAI;MAAAuG,QAAA,gBACHpE,OAAA;QAAK2H,SAAS,EAAC,SAAS;QAAAvD,QAAA,eACtBpE,OAAA;UAAK2H,SAAS,EAAC,cAAc;UAAAvD,QAAA,eAC3BpE,OAAA,CAACjC,MAAM;YACL6G,IAAI,EAAC,SAAS;YACde,IAAI,eAAE3F,OAAA,CAACb,YAAY;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBoB,OAAO,EAAEU,kBAAmB;YAAAlC,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxE,OAAA,CAAChC,KAAK;QACJmK,OAAO,EAAEpE,aAAc;QACvBqE,UAAU,EAAEzH,OAAQ;QACpB0H,MAAM,EAAC,IAAI;QACXtH,OAAO,EAAEA,OAAQ;QACjBuH,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC,CAAE;QACFC,UAAU,EAAE;UACVC,iBAAiB,EAAGpC,MAAM,IAAK;YAC7B,MAAMqC,cAAc,GAAGnB,iBAAiB,CAAClB,MAAM,CAAChG,EAAE,CAAC;YACnD,oBACER,OAAA;cAAK8H,KAAK,EAAE;gBAAEgB,MAAM,EAAE;cAAE,CAAE;cAAA1E,QAAA,gBACxBpE,OAAA,CAAChB,OAAO;gBAAC+J,WAAW,EAAC,MAAM;gBAAA3E,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC1CxE,OAAA,CAACjB,IAAI;gBACH4F,IAAI,EAAC,OAAO;gBACZyD,UAAU,EAAES,cAAe;gBAC3BG,UAAU,EAAGC,OAAO,iBAClBjJ,OAAA,CAACjB,IAAI,CAACmK,IAAI;kBACRC,OAAO,EAAE,cACPnJ,OAAA,CAACjC,MAAM;oBACL6G,IAAI,EAAC,MAAM;oBACXD,IAAI,EAAC,OAAO;oBACZiB,OAAO,EAAEA,CAAA,KAAMlF,QAAQ,CAAC,aAAaD,SAAS,YAAY+F,MAAM,CAAChG,EAAE,aAAayI,OAAO,CAACzI,EAAE,EAAE,CAAE;oBAAA4D,QAAA,EAC/F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTxE,OAAA,CAACjC,MAAM;oBACL6G,IAAI,EAAC,MAAM;oBACXD,IAAI,EAAC,OAAO;oBACZiB,OAAO,EAAEA,CAAA,KAAMlF,QAAQ,CAAC,aAAaD,SAAS,YAAY+F,MAAM,CAAChG,EAAE,aAAayI,OAAO,CAACzI,EAAE,EAAE,CAAE;oBAAA4D,QAAA,EAC/F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,CACT;kBAAAJ,QAAA,eAEFpE,OAAA,CAACjB,IAAI,CAACmK,IAAI,CAACE,IAAI;oBACbC,MAAM,eAAErJ,OAAA,CAACZ,gBAAgB;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC7B9B,KAAK,eACH1C,OAAA,CAAC/B,KAAK;sBAAAmG,QAAA,GACH6E,OAAO,CAACvG,KAAK,eACd1C,OAAA,CAAC9B,GAAG;wBAAC8D,KAAK,EAAEO,mBAAmB,CAAC0G,OAAO,CAACrG,MAAM,CAAC,CAACZ,KAAM;wBAAAoC,QAAA,EACnD7B,mBAAmB,CAAC0G,OAAO,CAACrG,MAAM,CAAC,CAACX;sBAAI;wBAAAoC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACR;oBACD8E,WAAW,EAAE,OAAOL,OAAO,CAACxF,SAAS,UAAUwF,OAAO,CAAC7F,SAAS;kBAAG;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cACX;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDqE,cAAc,CAAClF,MAAM,KAAK,CAAC,iBAC1B3D,OAAA;gBAAK8H,KAAK,EAAE;kBAAEyB,SAAS,EAAE,QAAQ;kBAAEC,OAAO,EAAE,MAAM;kBAAExH,KAAK,EAAE;gBAAO,CAAE;gBAAAoC,QAAA,EAAC;cAErE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAEV,CAAC;UACDiF,aAAa,EAAEA,CAAA,KAAM;QACvB;MAAE;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPxE,OAAA,CAAC7B,KAAK;MACJuE,KAAK,EAAErB,aAAa,GAAG,MAAM,GAAG,MAAO;MACvCqI,IAAI,EAAEzI,kBAAmB;MACzB0I,IAAI,EAAE5C,mBAAoB;MAC1B6C,QAAQ,EAAEA,CAAA,KAAM;QACd1I,qBAAqB,CAAC,KAAK,CAAC;QAC5BS,UAAU,CAAC4E,WAAW,CAAC,CAAC;MAC1B,CAAE;MACFsD,cAAc,EAAE9I,OAAQ;MACxB+I,KAAK,EAAE,GAAI;MAAA1F,QAAA,eAEXpE,OAAA,CAAC5B,IAAI;QACH2L,IAAI,EAAEpI,UAAW;QACjBqI,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UAAErH,MAAM,EAAE;QAAW,CAAE;QAAAwB,QAAA,gBAEtCpE,OAAA,CAACrB,GAAG;UAACkJ,MAAM,EAAE,EAAG;UAAAzD,QAAA,gBACdpE,OAAA,CAACpB,GAAG;YAACoJ,IAAI,EAAE,EAAG;YAAA5D,QAAA,eACZpE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;cACRgB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6F,QAAA,eAEhDpE,OAAA,CAAC3B,KAAK;gBAACiM,WAAW,EAAC;cAAS;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxE,OAAA,CAACpB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA5D,QAAA,eACXpE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;cACRgB,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9L,OAAO,EAAE;cAAS,CAAC,CAAE;cAAA6F,QAAA,eAE/CpE,OAAA,CAAC3B,KAAK;gBAACuG,IAAI,EAAC,QAAQ;gBAAC0F,WAAW,EAAC;cAAG;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;UACRgB,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9L,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6F,QAAA,eAEhDpE,OAAA,CAACG,QAAQ;YACPoK,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAmB;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZxE,OAAA,CAACrB,GAAG;UAACkJ,MAAM,EAAE,EAAG;UAAAzD,QAAA,gBACdpE,OAAA,CAACpB,GAAG;YAACoJ,IAAI,EAAE,EAAG;YAAA5D,QAAA,eACZpE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;cACRgB,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6F,QAAA,eAEhDpE,OAAA,CAAC1B,MAAM;gBAAA8F,QAAA,EACJe,MAAM,CAACC,OAAO,CAACtD,kBAAkB,CAAC,CAACuD,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,kBACpDtF,OAAA,CAACI,MAAM;kBAAWmF,KAAK,EAAEtB,GAAI;kBAAAG,QAAA,EAAEkB,MAAM,CAACrD;gBAAI,GAA7BgC,GAAG;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxE,OAAA,CAACpB,GAAG;YAACoJ,IAAI,EAAE,EAAG;YAAA5D,QAAA,eACZpE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;cACRgB,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAC,0BAAM;cAAA/F,QAAA,eAEZpE,OAAA,CAAC3B,KAAK;gBAACuG,IAAI,EAAC,QAAQ;gBAAC0F,WAAW,EAAC,OAAO;gBAACpC,MAAM,EAAC;cAAG;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;UACRgB,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,0BAAM;UAAA/F,QAAA,eAEZpE,OAAA,CAACG,QAAQ;YACPoK,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAqB;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRxE,OAAA,CAAC7B,KAAK;MACJuE,KAAK,EAAC,0BAAM;MACZgH,IAAI,EAAEvI,mBAAoB;MAC1BwI,IAAI,EAAEnC,oBAAqB;MAC3BoC,QAAQ,EAAEA,CAAA,KAAM;QACdxI,sBAAsB,CAAC,KAAK,CAAC;QAC7BS,WAAW,CAAC0E,WAAW,CAAC,CAAC;MAC3B,CAAE;MACFsD,cAAc,EAAE9I,OAAQ;MACxB+I,KAAK,EAAE,GAAI;MAAA1F,QAAA,eAEXpE,OAAA,CAAC5B,IAAI;QACH2L,IAAI,EAAElI,WAAY;QAClBmI,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UAAErH,MAAM,EAAE;QAAW,CAAE;QAAAwB,QAAA,gBAEtCpE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;UACRgB,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9L,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6F,QAAA,eAEhDpE,OAAA,CAAC1B,MAAM;YAACgM,WAAW,EAAC,gCAAO;YAAAlG,QAAA,EACxBzD,OAAO,CAAC0E,GAAG,CAACmB,MAAM,iBACjBxG,OAAA,CAACI,MAAM;cAAiBmF,KAAK,EAAEiB,MAAM,CAAChG,EAAG;cAAA4D,QAAA,EACtCoC,MAAM,CAAC9D;YAAK,GADF8D,MAAM,CAAChG,EAAE;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZxE,OAAA,CAACrB,GAAG;UAACkJ,MAAM,EAAE,EAAG;UAAAzD,QAAA,gBACdpE,OAAA,CAACpB,GAAG;YAACoJ,IAAI,EAAE,EAAG;YAAA5D,QAAA,eACZpE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;cACRgB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6F,QAAA,eAEhDpE,OAAA,CAAC3B,KAAK;gBAACiM,WAAW,EAAC;cAAS;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxE,OAAA,CAACpB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA5D,QAAA,eACXpE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;cACRgB,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9L,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6F,QAAA,eAEhDpE,OAAA,CAAC3B,KAAK;gBAACuG,IAAI,EAAC,QAAQ;gBAAC0F,WAAW,EAAC;cAAG;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;UACRgB,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9L,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6F,QAAA,eAEhDpE,OAAA,CAAC1B,MAAM;YAAA8F,QAAA,EACJe,MAAM,CAACC,OAAO,CAAC7C,mBAAmB,CAAC,CAAC8C,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,kBACrDtF,OAAA,CAACI,MAAM;cAAWmF,KAAK,EAAEtB,GAAI;cAAAG,QAAA,EAAEkB,MAAM,CAACrD;YAAI,GAA7BgC,GAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZxE,OAAA,CAAC5B,IAAI,CAAC8K,IAAI;UACRgB,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,0BAAM;UAAA/F,QAAA,eAEZpE,OAAA,CAACG,QAAQ;YACPoK,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAoB;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACjE,EAAA,CA5mBID,UAAU;EAAA,QACY3C,SAAS,EAClBC,WAAW,EASPQ,IAAI,CAACwD,OAAO,EACXxD,IAAI,CAACwD,OAAO;AAAA;AAAA4I,EAAA,GAZ9BlK,UAAU;AA8mBhB,eAAeA,UAAU;AAAC,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}