{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\VolumeList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Progress, Tooltip, Row, Col, Statistic, Collapse, List, Divider, Tabs, Empty } from 'antd';\nimport { PlusOutlined, FileTextOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, BookOutlined, ClockCircleOutlined, CheckCircleOutlined, FolderOutlined, OrderedListOutlined, BarChartOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  Panel\n} = Collapse;\nconst {\n  TabPane\n} = Tabs;\nconst VolumeList = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const navigate = useNavigate();\n  const [volumes, setVolumes] = useState([]);\n  const [chapters, setChapters] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [volumeModalVisible, setVolumeModalVisible] = useState(false);\n  const [chapterModalVisible, setChapterModalVisible] = useState(false);\n  const [editingVolume, setEditingVolume] = useState(null);\n  const [editingChapter, setEditingChapter] = useState(null);\n  const [selectedVolumeId, setSelectedVolumeId] = useState(null);\n  const [activeTab, setActiveTab] = useState('volumes');\n  const [volumeForm] = Form.useForm();\n  const [chapterForm] = Form.useForm();\n\n  // 状态配置\n  const volumeStatusConfig = {\n    planning: {\n      color: 'blue',\n      text: '规划中'\n    },\n    writing: {\n      color: 'orange',\n      text: '写作中'\n    },\n    completed: {\n      color: 'green',\n      text: '已完成'\n    },\n    reviewing: {\n      color: 'purple',\n      text: '审阅中'\n    },\n    revised: {\n      color: 'cyan',\n      text: '已修订'\n    },\n    published: {\n      color: 'success',\n      text: '已发布'\n    }\n  };\n  const chapterStatusConfig = {\n    planning: {\n      color: 'blue',\n      text: '规划中'\n    },\n    draft: {\n      color: 'orange',\n      text: '草稿'\n    },\n    writing: {\n      color: 'processing',\n      text: '写作中'\n    },\n    completed: {\n      color: 'green',\n      text: '已完成'\n    },\n    published: {\n      color: 'success',\n      text: '已发布'\n    }\n  };\n\n  // 模拟卷宗数据\n  const mockVolumes = [{\n    id: 1,\n    title: '第一卷：初入修仙界',\n    volumeNumber: 1,\n    status: 'writing',\n    summary: '主角初次踏入修仙世界，遇到师父，开始修炼之路',\n    totalChapters: 10,\n    completedChapters: 6,\n    totalWords: 45000,\n    targetWords: 80000,\n    progress: 60,\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-20'\n  }, {\n    id: 2,\n    title: '第二卷：宗门试炼',\n    volumeNumber: 2,\n    status: 'planning',\n    summary: '主角参加宗门入门试炼，展现天赋，结识同门',\n    totalChapters: 8,\n    completedChapters: 0,\n    totalWords: 0,\n    targetWords: 60000,\n    progress: 0,\n    createdAt: '2024-01-21',\n    updatedAt: '2024-01-21'\n  }];\n\n  // 模拟章节数据\n  const mockChapters = [{\n    id: 1,\n    volumeId: 1,\n    title: '第一章：觉醒',\n    chapterNumber: 1,\n    content: '在这个充满灵气的世界里，少年踏上了修仙之路...',\n    wordCount: 3500,\n    status: 'published',\n    outline: '主角初次接触修仙世界，遇到第一位师父',\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-16'\n  }, {\n    id: 2,\n    volumeId: 1,\n    title: '第二章：师父',\n    chapterNumber: 2,\n    content: '经过数月的修炼，主角终于感受到了灵气的存在...',\n    wordCount: 4200,\n    status: 'completed',\n    outline: '主角开始正式修炼，学习基础功法',\n    createdAt: '2024-01-17',\n    updatedAt: '2024-01-18'\n  }, {\n    id: 3,\n    volumeId: 1,\n    title: '第三章：修炼',\n    chapterNumber: 3,\n    content: '',\n    wordCount: 0,\n    status: 'planning',\n    outline: '主角参加宗门入门试炼，展现天赋',\n    createdAt: '2024-01-19',\n    updatedAt: '2024-01-19'\n  }, {\n    id: 4,\n    volumeId: 2,\n    title: '第四章：试炼开始',\n    chapterNumber: 1,\n    content: '',\n    wordCount: 0,\n    status: 'planning',\n    outline: '宗门试炼正式开始，各路天才汇聚',\n    createdAt: '2024-01-20',\n    updatedAt: '2024-01-20'\n  }, {\n    id: 5,\n    volumeId: null,\n    // 独立章节，未分配到卷宗\n    title: '番外：师父的过往',\n    chapterNumber: null,\n    content: '很久以前，师父也是一个普通的修炼者...',\n    wordCount: 2800,\n    status: 'draft',\n    outline: '讲述师父的背景故事',\n    createdAt: '2024-01-21',\n    updatedAt: '2024-01-21'\n  }];\n  useEffect(() => {\n    setVolumes(mockVolumes);\n    setChapters(mockChapters);\n  }, []);\n\n  // 统计数据\n  const totalVolumes = volumes.length;\n  const totalChapters = chapters.length;\n  const completedChapters = chapters.filter(c => c.status === 'completed' || c.status === 'published').length;\n  const totalWords = chapters.reduce((sum, chapter) => sum + (chapter.wordCount || 0), 0);\n  const independentChapters = chapters.filter(c => !c.volumeId).length;\n\n  // 卷宗表格列配置\n  const volumeColumns = [{\n    title: '卷宗标题',\n    dataIndex: 'title',\n    key: 'title',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: [\"\\u7B2C\", record.volumeNumber, \"\\u5377\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '进度',\n    dataIndex: 'progress',\n    key: 'progress',\n    render: (progress, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Progress, {\n        percent: progress,\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: [record.completedChapters, \"/\", record.totalChapters, \" \\u7AE0\\u8282\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.progress - b.progress\n  }, {\n    title: '字数',\n    dataIndex: 'totalWords',\n    key: 'totalWords',\n    render: (words, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        children: [words.toLocaleString(), \" \\u5B57\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), record.targetWords && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [\"\\u76EE\\u6807: \", record.targetWords.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.totalWords - b.totalWords\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: volumeStatusConfig[status].color,\n      children: volumeStatusConfig[status].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this),\n    filters: Object.entries(volumeStatusConfig).map(([key, config]) => ({\n      text: config.text,\n      value: key\n    })),\n    onFilter: (value, record) => record.status === value\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u7AE0\\u8282\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewVolumeChapters(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\\u5377\\u5B97\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEditVolume(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u6DFB\\u52A0\\u7AE0\\u8282\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAddChapter(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u751F\\u6210\\u5927\\u7EB2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIGenerateOutline(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u5377\\u5B97\\u5417\\uFF1F\\u8FD9\\u5C06\\u540C\\u65F6\\u5220\\u9664\\u5176\\u4E0B\\u6240\\u6709\\u7AE0\\u8282\\u3002\",\n        onConfirm: () => handleDeleteVolume(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理函数\n  const handleCreateVolume = () => {\n    setEditingVolume(null);\n    volumeForm.resetFields();\n    setVolumeModalVisible(true);\n  };\n  const handleEditVolume = volume => {\n    setEditingVolume(volume);\n    volumeForm.setFieldsValue(volume);\n    setVolumeModalVisible(true);\n  };\n  const handleViewVolumeChapters = volume => {\n    setSelectedVolumeId(volume.id);\n    message.info(`查看卷宗《${volume.title}》的章节`);\n  };\n  const handleAddChapter = volume => {\n    setSelectedVolumeId(volume.id);\n    setEditingChapter(null);\n    chapterForm.resetFields();\n    chapterForm.setFieldsValue({\n      volumeId: volume.id\n    });\n    setChapterModalVisible(true);\n  };\n  const handleAIGenerateOutline = volume => {\n    message.info(`AI生成卷宗《${volume.title}》大纲功能`);\n  };\n  const handleDeleteVolume = id => {\n    setVolumes(volumes.filter(v => v.id !== id));\n    setChapters(chapters.filter(c => c.volumeId !== id));\n    message.success('卷宗删除成功');\n  };\n  const handleVolumeModalOk = async () => {\n    try {\n      const values = await volumeForm.validateFields();\n      setLoading(true);\n      if (editingVolume) {\n        // 编辑卷宗\n        setVolumes(volumes.map(v => v.id === editingVolume.id ? {\n          ...v,\n          ...values,\n          updatedAt: new Date().toISOString().split('T')[0]\n        } : v));\n        message.success('卷宗更新成功');\n      } else {\n        // 新建卷宗\n        const newVolume = {\n          id: Date.now(),\n          ...values,\n          totalChapters: 0,\n          completedChapters: 0,\n          totalWords: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setVolumes([...volumes, newVolume]);\n        message.success('卷宗创建成功');\n      }\n      setVolumeModalVisible(false);\n      volumeForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChapterModalOk = async () => {\n    try {\n      const values = await chapterForm.validateFields();\n      setLoading(true);\n      const newChapter = {\n        id: Date.now(),\n        ...values,\n        wordCount: 0,\n        createdAt: new Date().toISOString().split('T')[0],\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n      setChapters([...chapters, newChapter]);\n      message.success('章节创建成功');\n      setChapterModalVisible(false);\n      chapterForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取指定卷宗的章节\n  const getVolumeChapters = volumeId => {\n    return chapters.filter(c => c.volumeId === volumeId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u5377\\u5B97\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5377\\u5B97\\u6570\",\n            value: totalVolumes,\n            prefix: /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u7AE0\\u8282\\u6570\",\n            value: totalChapters,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u7AE0\\u8282\",\n            value: completedChapters,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5B57\\u6570\",\n            value: totalWords,\n            suffix: \"\\u5B57\",\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreateVolume,\n            children: \"\\u65B0\\u5EFA\\u5377\\u5B97\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: volumeColumns,\n        dataSource: volumes,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个卷宗`\n        },\n        expandable: {\n          expandedRowRender: volume => {\n            const volumeChapters = getVolumeChapters(volume.id);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                margin: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(Divider, {\n                orientation: \"left\",\n                children: \"\\u7AE0\\u8282\\u5217\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                size: \"small\",\n                dataSource: volumeChapters,\n                renderItem: chapter => /*#__PURE__*/_jsxDEV(List.Item, {\n                  actions: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    size: \"small\",\n                    onClick: () => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`),\n                    children: \"\\u7F16\\u8F91\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    size: \"small\",\n                    onClick: () => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`),\n                    children: \"\\u67E5\\u770B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 27\n                  }, this)],\n                  children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                    avatar: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 35\n                    }, this),\n                    title: /*#__PURE__*/_jsxDEV(Space, {\n                      children: [chapter.title, /*#__PURE__*/_jsxDEV(Tag, {\n                        color: chapterStatusConfig[chapter.status].color,\n                        children: chapterStatusConfig[chapter.status].text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 512,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 29\n                    }, this),\n                    description: `字数: ${chapter.wordCount} | 更新: ${chapter.updatedAt}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this), volumeChapters.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  padding: '20px',\n                  color: '#999'\n                },\n                children: \"\\u6682\\u65E0\\u7AE0\\u8282\\uFF0C\\u70B9\\u51FB\\u4E0A\\u65B9\\\"\\u6DFB\\u52A0\\u7AE0\\u8282\\\"\\u6309\\u94AE\\u521B\\u5EFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this);\n          },\n          rowExpandable: () => true\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingVolume ? '编辑卷宗' : '新建卷宗',\n      open: volumeModalVisible,\n      onOk: handleVolumeModalOk,\n      onCancel: () => {\n        setVolumeModalVisible(false);\n        volumeForm.resetFields();\n      },\n      confirmLoading: loading,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: volumeForm,\n        layout: \"vertical\",\n        initialValues: {\n          status: 'planning'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u5377\\u5B97\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入卷宗标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5377\\u5B97\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"volumeNumber\",\n              label: \"\\u5377\\u5E8F\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入卷序号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"summary\",\n          label: \"\\u5377\\u5B97\\u6458\\u8981\",\n          rules: [{\n            required: true,\n            message: '请输入卷宗摘要'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u7B80\\u8981\\u63CF\\u8FF0\\u672C\\u5377\\u7684\\u4E3B\\u8981\\u5185\\u5BB9\\u548C\\u5267\\u60C5\\u53D1\\u5C55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择卷宗状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: Object.entries(volumeStatusConfig).map(([key, config]) => /*#__PURE__*/_jsxDEV(Option, {\n                  value: key,\n                  children: config.text\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"targetWords\",\n              label: \"\\u76EE\\u6807\\u5B57\\u6570\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"80000\",\n                suffix: \"\\u5B57\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"outline\",\n          label: \"\\u5377\\u5B97\\u5927\\u7EB2\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 6,\n            placeholder: \"\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u672C\\u5377\\u7684\\u7AE0\\u8282\\u5B89\\u6392\\u548C\\u5267\\u60C5\\u53D1\\u5C55...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u65B0\\u5EFA\\u7AE0\\u8282\",\n      open: chapterModalVisible,\n      onOk: handleChapterModalOk,\n      onCancel: () => {\n        setChapterModalVisible(false);\n        chapterForm.resetFields();\n      },\n      confirmLoading: loading,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: chapterForm,\n        layout: \"vertical\",\n        initialValues: {\n          status: 'planning'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"volumeId\",\n          label: \"\\u6240\\u5C5E\\u5377\\u5B97\",\n          rules: [{\n            required: true,\n            message: '请选择所属卷宗'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5377\\u5B97\",\n            children: volumes.map(volume => /*#__PURE__*/_jsxDEV(Option, {\n              value: volume.id,\n              children: volume.title\n            }, volume.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u7AE0\\u8282\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入章节标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7AE0\\u8282\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"chapterNumber\",\n              label: \"\\u7AE0\\u8282\\u5E8F\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入章节序号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择章节状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: Object.entries(chapterStatusConfig).map(([key, config]) => /*#__PURE__*/_jsxDEV(Option, {\n              value: key,\n              children: config.text\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"outline\",\n          label: \"\\u7AE0\\u8282\\u5927\\u7EB2\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u7B80\\u8981\\u63CF\\u8FF0\\u672C\\u7AE0\\u8282\\u7684\\u4E3B\\u8981\\u5185\\u5BB9\\u548C\\u60C5\\u8282\\u53D1\\u5C55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 410,\n    columnNumber: 5\n  }, this);\n};\n_s(VolumeList, \"XNoP3UbuRnqEB9r45Mv78P7dZDs=\", false, function () {\n  return [useParams, useNavigate, Form.useForm, Form.useForm];\n});\n_c = VolumeList;\nexport default VolumeList;\nvar _c;\n$RefreshReg$(_c, \"VolumeList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Progress", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Collapse", "List", "Divider", "Tabs", "Empty", "PlusOutlined", "FileTextOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "BookOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "FolderOutlined", "OrderedListOutlined", "BarChartOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "Panel", "TabPane", "VolumeList", "_s", "id", "projectId", "navigate", "volumes", "setVolumes", "chapters", "setChapters", "loading", "setLoading", "volumeModalVisible", "setVolumeModalVisible", "chapterModalVisible", "setChapterModalVisible", "editingVolume", "setEditingVolume", "editing<PERSON><PERSON>pter", "setEditingChapter", "selectedVolumeId", "setSelectedVolumeId", "activeTab", "setActiveTab", "volumeForm", "useForm", "chapterForm", "volumeStatusConfig", "planning", "color", "text", "writing", "completed", "reviewing", "revised", "published", "chapterStatusConfig", "draft", "mockVolumes", "title", "volumeNumber", "status", "summary", "totalChapters", "completedChapters", "totalWords", "targetWords", "progress", "createdAt", "updatedAt", "mockChapters", "volumeId", "chapterNumber", "content", "wordCount", "outline", "totalVolumes", "length", "filter", "c", "reduce", "sum", "chapter", "independentChapters", "volumeColumns", "dataIndex", "key", "render", "record", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strong", "percent", "size", "type", "sorter", "a", "b", "words", "toLocaleString", "filters", "Object", "entries", "map", "config", "value", "onFilter", "Date", "_", "icon", "onClick", "handleViewVolumeChapters", "handleEditVolume", "handleAddChapter", "handleAIGenerateOutline", "onConfirm", "handleDeleteVolume", "okText", "cancelText", "danger", "handleCreateVolume", "resetFields", "volume", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "v", "success", "handleVolumeModalOk", "values", "validateFields", "toISOString", "split", "newVolume", "now", "error", "console", "handleChapterModalOk", "newChapter", "getVolumeChapters", "className", "level", "gutter", "style", "marginBottom", "span", "prefix", "suffix", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "expandable", "expandedRowRender", "volumeChapters", "margin", "orientation", "renderItem", "<PERSON><PERSON>", "actions", "Meta", "avatar", "description", "textAlign", "padding", "rowExpandable", "open", "onOk", "onCancel", "confirmLoading", "width", "form", "layout", "initialValues", "name", "label", "rules", "required", "placeholder", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/VolumeList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Progress,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Collapse,\n  List,\n  Divider,\n  Tabs,\n  Empty\n} from 'antd';\nimport {\n  PlusOutlined,\n  FileTextOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  BookOutlined,\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  FolderOutlined,\n  OrderedListOutlined,\n  BarChartOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { Panel } = Collapse;\nconst { TabPane } = Tabs;\n\nconst VolumeList = () => {\n  const { id: projectId } = useParams();\n  const navigate = useNavigate();\n  const [volumes, setVolumes] = useState([]);\n  const [chapters, setChapters] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [volumeModalVisible, setVolumeModalVisible] = useState(false);\n  const [chapterModalVisible, setChapterModalVisible] = useState(false);\n  const [editingVolume, setEditingVolume] = useState(null);\n  const [editingChapter, setEditingChapter] = useState(null);\n  const [selectedVolumeId, setSelectedVolumeId] = useState(null);\n  const [activeTab, setActiveTab] = useState('volumes');\n  const [volumeForm] = Form.useForm();\n  const [chapterForm] = Form.useForm();\n\n  // 状态配置\n  const volumeStatusConfig = {\n    planning: { color: 'blue', text: '规划中' },\n    writing: { color: 'orange', text: '写作中' },\n    completed: { color: 'green', text: '已完成' },\n    reviewing: { color: 'purple', text: '审阅中' },\n    revised: { color: 'cyan', text: '已修订' },\n    published: { color: 'success', text: '已发布' }\n  };\n\n  const chapterStatusConfig = {\n    planning: { color: 'blue', text: '规划中' },\n    draft: { color: 'orange', text: '草稿' },\n    writing: { color: 'processing', text: '写作中' },\n    completed: { color: 'green', text: '已完成' },\n    published: { color: 'success', text: '已发布' }\n  };\n\n  // 模拟卷宗数据\n  const mockVolumes = [\n    {\n      id: 1,\n      title: '第一卷：初入修仙界',\n      volumeNumber: 1,\n      status: 'writing',\n      summary: '主角初次踏入修仙世界，遇到师父，开始修炼之路',\n      totalChapters: 10,\n      completedChapters: 6,\n      totalWords: 45000,\n      targetWords: 80000,\n      progress: 60,\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-20'\n    },\n    {\n      id: 2,\n      title: '第二卷：宗门试炼',\n      volumeNumber: 2,\n      status: 'planning',\n      summary: '主角参加宗门入门试炼，展现天赋，结识同门',\n      totalChapters: 8,\n      completedChapters: 0,\n      totalWords: 0,\n      targetWords: 60000,\n      progress: 0,\n      createdAt: '2024-01-21',\n      updatedAt: '2024-01-21'\n    }\n  ];\n\n  // 模拟章节数据\n  const mockChapters = [\n    {\n      id: 1,\n      volumeId: 1,\n      title: '第一章：觉醒',\n      chapterNumber: 1,\n      content: '在这个充满灵气的世界里，少年踏上了修仙之路...',\n      wordCount: 3500,\n      status: 'published',\n      outline: '主角初次接触修仙世界，遇到第一位师父',\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-16'\n    },\n    {\n      id: 2,\n      volumeId: 1,\n      title: '第二章：师父',\n      chapterNumber: 2,\n      content: '经过数月的修炼，主角终于感受到了灵气的存在...',\n      wordCount: 4200,\n      status: 'completed',\n      outline: '主角开始正式修炼，学习基础功法',\n      createdAt: '2024-01-17',\n      updatedAt: '2024-01-18'\n    },\n    {\n      id: 3,\n      volumeId: 1,\n      title: '第三章：修炼',\n      chapterNumber: 3,\n      content: '',\n      wordCount: 0,\n      status: 'planning',\n      outline: '主角参加宗门入门试炼，展现天赋',\n      createdAt: '2024-01-19',\n      updatedAt: '2024-01-19'\n    },\n    {\n      id: 4,\n      volumeId: 2,\n      title: '第四章：试炼开始',\n      chapterNumber: 1,\n      content: '',\n      wordCount: 0,\n      status: 'planning',\n      outline: '宗门试炼正式开始，各路天才汇聚',\n      createdAt: '2024-01-20',\n      updatedAt: '2024-01-20'\n    },\n    {\n      id: 5,\n      volumeId: null, // 独立章节，未分配到卷宗\n      title: '番外：师父的过往',\n      chapterNumber: null,\n      content: '很久以前，师父也是一个普通的修炼者...',\n      wordCount: 2800,\n      status: 'draft',\n      outline: '讲述师父的背景故事',\n      createdAt: '2024-01-21',\n      updatedAt: '2024-01-21'\n    }\n  ];\n\n  useEffect(() => {\n    setVolumes(mockVolumes);\n    setChapters(mockChapters);\n  }, []);\n\n  // 统计数据\n  const totalVolumes = volumes.length;\n  const totalChapters = chapters.length;\n  const completedChapters = chapters.filter(c => c.status === 'completed' || c.status === 'published').length;\n  const totalWords = chapters.reduce((sum, chapter) => sum + (chapter.wordCount || 0), 0);\n  const independentChapters = chapters.filter(c => !c.volumeId).length;\n\n  // 卷宗表格列配置\n  const volumeColumns = [\n    {\n      title: '卷宗标题',\n      dataIndex: 'title',\n      key: 'title',\n      render: (text, record) => (\n        <Space>\n          <FolderOutlined />\n          <Text strong>{text}</Text>\n          <Tag color=\"blue\">第{record.volumeNumber}卷</Tag>\n        </Space>\n      )\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      render: (progress, record) => (\n        <div>\n          <Progress percent={progress} size=\"small\" />\n          <Text type=\"secondary\">\n            {record.completedChapters}/{record.totalChapters} 章节\n          </Text>\n        </div>\n      ),\n      sorter: (a, b) => a.progress - b.progress\n    },\n    {\n      title: '字数',\n      dataIndex: 'totalWords',\n      key: 'totalWords',\n      render: (words, record) => (\n        <div>\n          <Text>{words.toLocaleString()} 字</Text>\n          {record.targetWords && (\n            <div>\n              <Text type=\"secondary\">目标: {record.targetWords.toLocaleString()}</Text>\n            </div>\n          )}\n        </div>\n      ),\n      sorter: (a, b) => a.totalWords - b.totalWords\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={volumeStatusConfig[status].color}>\n          {volumeStatusConfig[status].text}\n        </Tag>\n      ),\n      filters: Object.entries(volumeStatusConfig).map(([key, config]) => ({\n        text: config.text,\n        value: key\n      })),\n      onFilter: (value, record) => record.status === value\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看章节\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewVolumeChapters(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑卷宗\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditVolume(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"添加章节\">\n            <Button\n              type=\"text\"\n              icon={<PlusOutlined />}\n              onClick={() => handleAddChapter(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI生成大纲\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIGenerateOutline(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个卷宗吗？这将同时删除其下所有章节。\"\n            onConfirm={() => handleDeleteVolume(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 处理函数\n  const handleCreateVolume = () => {\n    setEditingVolume(null);\n    volumeForm.resetFields();\n    setVolumeModalVisible(true);\n  };\n\n  const handleEditVolume = (volume) => {\n    setEditingVolume(volume);\n    volumeForm.setFieldsValue(volume);\n    setVolumeModalVisible(true);\n  };\n\n  const handleViewVolumeChapters = (volume) => {\n    setSelectedVolumeId(volume.id);\n    message.info(`查看卷宗《${volume.title}》的章节`);\n  };\n\n  const handleAddChapter = (volume) => {\n    setSelectedVolumeId(volume.id);\n    setEditingChapter(null);\n    chapterForm.resetFields();\n    chapterForm.setFieldsValue({ volumeId: volume.id });\n    setChapterModalVisible(true);\n  };\n\n  const handleAIGenerateOutline = (volume) => {\n    message.info(`AI生成卷宗《${volume.title}》大纲功能`);\n  };\n\n  const handleDeleteVolume = (id) => {\n    setVolumes(volumes.filter(v => v.id !== id));\n    setChapters(chapters.filter(c => c.volumeId !== id));\n    message.success('卷宗删除成功');\n  };\n\n  const handleVolumeModalOk = async () => {\n    try {\n      const values = await volumeForm.validateFields();\n      setLoading(true);\n\n      if (editingVolume) {\n        // 编辑卷宗\n        setVolumes(volumes.map(v =>\n          v.id === editingVolume.id\n            ? { ...v, ...values, updatedAt: new Date().toISOString().split('T')[0] }\n            : v\n        ));\n        message.success('卷宗更新成功');\n      } else {\n        // 新建卷宗\n        const newVolume = {\n          id: Date.now(),\n          ...values,\n          totalChapters: 0,\n          completedChapters: 0,\n          totalWords: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setVolumes([...volumes, newVolume]);\n        message.success('卷宗创建成功');\n      }\n\n      setVolumeModalVisible(false);\n      volumeForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChapterModalOk = async () => {\n    try {\n      const values = await chapterForm.validateFields();\n      setLoading(true);\n\n      const newChapter = {\n        id: Date.now(),\n        ...values,\n        wordCount: 0,\n        createdAt: new Date().toISOString().split('T')[0],\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n      setChapters([...chapters, newChapter]);\n      message.success('章节创建成功');\n\n      setChapterModalVisible(false);\n      chapterForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取指定卷宗的章节\n  const getVolumeChapters = (volumeId) => {\n    return chapters.filter(c => c.volumeId === volumeId);\n  };\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">卷宗管理</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总卷宗数\"\n              value={totalVolumes}\n              prefix={<FolderOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总章节数\"\n              value={totalChapters}\n              prefix={<BookOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已完成章节\"\n              value={completedChapters}\n              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总字数\"\n              value={totalWords}\n              suffix=\"字\"\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateVolume}\n            >\n              新建卷宗\n            </Button>\n          </div>\n        </div>\n\n        <Table\n          columns={volumeColumns}\n          dataSource={volumes}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个卷宗`\n          }}\n          expandable={{\n            expandedRowRender: (volume) => {\n              const volumeChapters = getVolumeChapters(volume.id);\n              return (\n                <div style={{ margin: 0 }}>\n                  <Divider orientation=\"left\">章节列表</Divider>\n                  <List\n                    size=\"small\"\n                    dataSource={volumeChapters}\n                    renderItem={(chapter) => (\n                      <List.Item\n                        actions={[\n                          <Button\n                            type=\"link\"\n                            size=\"small\"\n                            onClick={() => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`)}\n                          >\n                            编辑\n                          </Button>,\n                          <Button\n                            type=\"link\"\n                            size=\"small\"\n                            onClick={() => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`)}\n                          >\n                            查看\n                          </Button>\n                        ]}\n                      >\n                        <List.Item.Meta\n                          avatar={<FileTextOutlined />}\n                          title={\n                            <Space>\n                              {chapter.title}\n                              <Tag color={chapterStatusConfig[chapter.status].color}>\n                                {chapterStatusConfig[chapter.status].text}\n                              </Tag>\n                            </Space>\n                          }\n                          description={`字数: ${chapter.wordCount} | 更新: ${chapter.updatedAt}`}\n                        />\n                      </List.Item>\n                    )}\n                  />\n                  {volumeChapters.length === 0 && (\n                    <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>\n                      暂无章节，点击上方\"添加章节\"按钮创建\n                    </div>\n                  )}\n                </div>\n              );\n            },\n            rowExpandable: () => true,\n          }}\n        />\n      </Card>\n\n      {/* 新建/编辑卷宗模态框 */}\n      <Modal\n        title={editingVolume ? '编辑卷宗' : '新建卷宗'}\n        open={volumeModalVisible}\n        onOk={handleVolumeModalOk}\n        onCancel={() => {\n          setVolumeModalVisible(false);\n          volumeForm.resetFields();\n        }}\n        confirmLoading={loading}\n        width={800}\n      >\n        <Form\n          form={volumeForm}\n          layout=\"vertical\"\n          initialValues={{ status: 'planning' }}\n        >\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"卷宗标题\"\n                rules={[{ required: true, message: '请输入卷宗标题' }]}\n              >\n                <Input placeholder=\"请输入卷宗标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"volumeNumber\"\n                label=\"卷序号\"\n                rules={[{ required: true, message: '请输入卷序号' }]}\n              >\n                <Input type=\"number\" placeholder=\"1\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"summary\"\n            label=\"卷宗摘要\"\n            rules={[{ required: true, message: '请输入卷宗摘要' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请简要描述本卷的主要内容和剧情发展\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择卷宗状态' }]}\n              >\n                <Select>\n                  {Object.entries(volumeStatusConfig).map(([key, config]) => (\n                    <Option key={key} value={key}>{config.text}</Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"targetWords\"\n                label=\"目标字数\"\n              >\n                <Input type=\"number\" placeholder=\"80000\" suffix=\"字\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"outline\"\n            label=\"卷宗大纲\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"详细描述本卷的章节安排和剧情发展...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 新建章节模态框 */}\n      <Modal\n        title=\"新建章节\"\n        open={chapterModalVisible}\n        onOk={handleChapterModalOk}\n        onCancel={() => {\n          setChapterModalVisible(false);\n          chapterForm.resetFields();\n        }}\n        confirmLoading={loading}\n        width={600}\n      >\n        <Form\n          form={chapterForm}\n          layout=\"vertical\"\n          initialValues={{ status: 'planning' }}\n        >\n          <Form.Item\n            name=\"volumeId\"\n            label=\"所属卷宗\"\n            rules={[{ required: true, message: '请选择所属卷宗' }]}\n          >\n            <Select placeholder=\"请选择卷宗\">\n              {volumes.map(volume => (\n                <Option key={volume.id} value={volume.id}>\n                  {volume.title}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"章节标题\"\n                rules={[{ required: true, message: '请输入章节标题' }]}\n              >\n                <Input placeholder=\"请输入章节标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"chapterNumber\"\n                label=\"章节序号\"\n                rules={[{ required: true, message: '请输入章节序号' }]}\n              >\n                <Input type=\"number\" placeholder=\"1\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            rules={[{ required: true, message: '请选择章节状态' }]}\n          >\n            <Select>\n              {Object.entries(chapterStatusConfig).map(([key, config]) => (\n                <Option key={key} value={key}>{config.text}</Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"outline\"\n            label=\"章节大纲\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请简要描述本章节的主要内容和情节发展\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default VolumeList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,mBAAmB,EACnBC,gBAAgB,QACX,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpC,UAAU;AAClC,MAAM;EAAEqC;AAAS,CAAC,GAAG9B,KAAK;AAC1B,MAAM;EAAE+B;AAAO,CAAC,GAAG9B,MAAM;AACzB,MAAM;EAAE+B;AAAM,CAAC,GAAGvB,QAAQ;AAC1B,MAAM;EAAEwB;AAAQ,CAAC,GAAGrB,IAAI;AAExB,MAAMsB,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAG/C,SAAS,CAAC,CAAC;EACrC,MAAMgD,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACqE,UAAU,CAAC,GAAG1D,IAAI,CAAC2D,OAAO,CAAC,CAAC;EACnC,MAAM,CAACC,WAAW,CAAC,GAAG5D,IAAI,CAAC2D,OAAO,CAAC,CAAC;;EAEpC;EACA,MAAME,kBAAkB,GAAG;IACzBC,QAAQ,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACxCC,OAAO,EAAE;MAAEF,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAC;IACzCE,SAAS,EAAE;MAAEH,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1CG,SAAS,EAAE;MAAEJ,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC3CI,OAAO,EAAE;MAAEL,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACvCK,SAAS,EAAE;MAAEN,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM;EAC7C,CAAC;EAED,MAAMM,mBAAmB,GAAG;IAC1BR,QAAQ,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACxCO,KAAK,EAAE;MAAER,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAC;IACtCC,OAAO,EAAE;MAAEF,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC7CE,SAAS,EAAE;MAAEH,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1CK,SAAS,EAAE;MAAEN,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM;EAC7C,CAAC;;EAED;EACA,MAAMQ,WAAW,GAAG,CAClB;IACEnC,EAAE,EAAE,CAAC;IACLoC,KAAK,EAAE,WAAW;IAClBC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,wBAAwB;IACjCC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,CAAC;IACpBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE9C,EAAE,EAAE,CAAC;IACLoC,KAAK,EAAE,UAAU;IACjBC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE,sBAAsB;IAC/BC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE,CAAC;IACpBC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IACE/C,EAAE,EAAE,CAAC;IACLgD,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,IAAI;IACfb,MAAM,EAAE,WAAW;IACnBc,OAAO,EAAE,oBAAoB;IAC7BP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE9C,EAAE,EAAE,CAAC;IACLgD,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,IAAI;IACfb,MAAM,EAAE,WAAW;IACnBc,OAAO,EAAE,iBAAiB;IAC1BP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE9C,EAAE,EAAE,CAAC;IACLgD,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,CAAC;IACZb,MAAM,EAAE,UAAU;IAClBc,OAAO,EAAE,iBAAiB;IAC1BP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE9C,EAAE,EAAE,CAAC;IACLgD,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,UAAU;IACjBa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,CAAC;IACZb,MAAM,EAAE,UAAU;IAClBc,OAAO,EAAE,iBAAiB;IAC1BP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE9C,EAAE,EAAE,CAAC;IACLgD,QAAQ,EAAE,IAAI;IAAE;IAChBZ,KAAK,EAAE,UAAU;IACjBa,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE,sBAAsB;IAC/BC,SAAS,EAAE,IAAI;IACfb,MAAM,EAAE,OAAO;IACfc,OAAO,EAAE,WAAW;IACpBP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EAED7F,SAAS,CAAC,MAAM;IACdmD,UAAU,CAAC+B,WAAW,CAAC;IACvB7B,WAAW,CAACyC,YAAY,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,YAAY,GAAGlD,OAAO,CAACmD,MAAM;EACnC,MAAMd,aAAa,GAAGnC,QAAQ,CAACiD,MAAM;EACrC,MAAMb,iBAAiB,GAAGpC,QAAQ,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,MAAM,KAAK,WAAW,IAAIkB,CAAC,CAAClB,MAAM,KAAK,WAAW,CAAC,CAACgB,MAAM;EAC3G,MAAMZ,UAAU,GAAGrC,QAAQ,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,IAAIC,OAAO,CAACR,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACvF,MAAMS,mBAAmB,GAAGvD,QAAQ,CAACkD,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACR,QAAQ,CAAC,CAACM,MAAM;;EAEpE;EACA,MAAMO,aAAa,GAAG,CACpB;IACEzB,KAAK,EAAE,MAAM;IACb0B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACrC,IAAI,EAAEsC,MAAM,kBACnB1E,OAAA,CAAC/B,KAAK;MAAA0G,QAAA,gBACJ3E,OAAA,CAACJ,cAAc;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClB/E,OAAA,CAACE,IAAI;QAAC8E,MAAM;QAAAL,QAAA,EAAEvC;MAAI;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1B/E,OAAA,CAAC9B,GAAG;QAACiE,KAAK,EAAC,MAAM;QAAAwC,QAAA,GAAC,QAAC,EAACD,MAAM,CAAC5B,YAAY,EAAC,QAAC;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C;EAEX,CAAC,EACD;IACElC,KAAK,EAAE,IAAI;IACX0B,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAEA,CAACpB,QAAQ,EAAEqB,MAAM,kBACvB1E,OAAA;MAAA2E,QAAA,gBACE3E,OAAA,CAACvB,QAAQ;QAACwG,OAAO,EAAE5B,QAAS;QAAC6B,IAAI,EAAC;MAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5C/E,OAAA,CAACE,IAAI;QAACiF,IAAI,EAAC,WAAW;QAAAR,QAAA,GACnBD,MAAM,CAACxB,iBAAiB,EAAC,GAAC,EAACwB,MAAM,CAACzB,aAAa,EAAC,eACnD;MAAA;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;IACDK,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChC,QAAQ,GAAGiC,CAAC,CAACjC;EACnC,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACX0B,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACc,KAAK,EAAEb,MAAM,kBACpB1E,OAAA;MAAA2E,QAAA,gBACE3E,OAAA,CAACE,IAAI;QAAAyE,QAAA,GAAEY,KAAK,CAACC,cAAc,CAAC,CAAC,EAAC,SAAE;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACtCL,MAAM,CAACtB,WAAW,iBACjBpD,OAAA;QAAA2E,QAAA,eACE3E,OAAA,CAACE,IAAI;UAACiF,IAAI,EAAC,WAAW;UAAAR,QAAA,GAAC,gBAAI,EAACD,MAAM,CAACtB,WAAW,CAACoC,cAAc,CAAC,CAAC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;IACDK,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClC,UAAU,GAAGmC,CAAC,CAACnC;EACrC,CAAC,EACD;IACEN,KAAK,EAAE,IAAI;IACX0B,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAG1B,MAAM,iBACb/C,OAAA,CAAC9B,GAAG;MAACiE,KAAK,EAAEF,kBAAkB,CAACc,MAAM,CAAC,CAACZ,KAAM;MAAAwC,QAAA,EAC1C1C,kBAAkB,CAACc,MAAM,CAAC,CAACX;IAAI;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACN;IACDU,OAAO,EAAEC,MAAM,CAACC,OAAO,CAAC1D,kBAAkB,CAAC,CAAC2D,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,MAAM;MAClEzD,IAAI,EAAEyD,MAAM,CAACzD,IAAI;MACjB0D,KAAK,EAAEtB;IACT,CAAC,CAAC,CAAC;IACHuB,QAAQ,EAAEA,CAACD,KAAK,EAAEpB,MAAM,KAAKA,MAAM,CAAC3B,MAAM,KAAK+C;EACjD,CAAC,EACD;IACEjD,KAAK,EAAE,MAAM;IACb0B,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBY,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIU,IAAI,CAACX,CAAC,CAAC9B,SAAS,CAAC,GAAG,IAAIyC,IAAI,CAACV,CAAC,CAAC/B,SAAS;EAChE,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACX2B,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACwB,CAAC,EAAEvB,MAAM,kBAChB1E,OAAA,CAAC/B,KAAK;MAAA0G,QAAA,gBACJ3E,OAAA,CAACtB,OAAO;QAACmE,KAAK,EAAC,0BAAM;QAAA8B,QAAA,eACnB3E,OAAA,CAACjC,MAAM;UACLoH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAElG,OAAA,CAACT,WAAW;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBoB,OAAO,EAAEA,CAAA,KAAMC,wBAAwB,CAAC1B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV/E,OAAA,CAACtB,OAAO;QAACmE,KAAK,EAAC,0BAAM;QAAA8B,QAAA,eACnB3E,OAAA,CAACjC,MAAM;UACLoH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAElG,OAAA,CAACX,YAAY;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoB,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAAC3B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV/E,OAAA,CAACtB,OAAO;QAACmE,KAAK,EAAC,0BAAM;QAAA8B,QAAA,eACnB3E,OAAA,CAACjC,MAAM;UACLoH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAElG,OAAA,CAACb,YAAY;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoB,OAAO,EAAEA,CAAA,KAAMG,gBAAgB,CAAC5B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV/E,OAAA,CAACtB,OAAO;QAACmE,KAAK,EAAC,4BAAQ;QAAA8B,QAAA,eACrB3E,OAAA,CAACjC,MAAM;UACLoH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAElG,OAAA,CAACR,aAAa;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBoB,OAAO,EAAEA,CAAA,KAAMI,uBAAuB,CAAC7B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV/E,OAAA,CAACxB,UAAU;QACTqE,KAAK,EAAC,4IAAyB;QAC/B2D,SAAS,EAAEA,CAAA,KAAMC,kBAAkB,CAAC/B,MAAM,CAACjE,EAAE,CAAE;QAC/CiG,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAhC,QAAA,eAEf3E,OAAA,CAACtB,OAAO;UAACmE,KAAK,EAAC,cAAI;UAAA8B,QAAA,eACjB3E,OAAA,CAACjC,MAAM;YACLoH,IAAI,EAAC,MAAM;YACXyB,MAAM;YACNV,IAAI,eAAElG,OAAA,CAACV,cAAc;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAM8B,kBAAkB,GAAGA,CAAA,KAAM;IAC/BtF,gBAAgB,CAAC,IAAI,CAAC;IACtBO,UAAU,CAACgF,WAAW,CAAC,CAAC;IACxB3F,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMkF,gBAAgB,GAAIU,MAAM,IAAK;IACnCxF,gBAAgB,CAACwF,MAAM,CAAC;IACxBjF,UAAU,CAACkF,cAAc,CAACD,MAAM,CAAC;IACjC5F,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMiF,wBAAwB,GAAIW,MAAM,IAAK;IAC3CpF,mBAAmB,CAACoF,MAAM,CAACtG,EAAE,CAAC;IAC9BlC,OAAO,CAAC0I,IAAI,CAAC,QAAQF,MAAM,CAAClE,KAAK,MAAM,CAAC;EAC1C,CAAC;EAED,MAAMyD,gBAAgB,GAAIS,MAAM,IAAK;IACnCpF,mBAAmB,CAACoF,MAAM,CAACtG,EAAE,CAAC;IAC9BgB,iBAAiB,CAAC,IAAI,CAAC;IACvBO,WAAW,CAAC8E,WAAW,CAAC,CAAC;IACzB9E,WAAW,CAACgF,cAAc,CAAC;MAAEvD,QAAQ,EAAEsD,MAAM,CAACtG;IAAG,CAAC,CAAC;IACnDY,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMkF,uBAAuB,GAAIQ,MAAM,IAAK;IAC1CxI,OAAO,CAAC0I,IAAI,CAAC,UAAUF,MAAM,CAAClE,KAAK,OAAO,CAAC;EAC7C,CAAC;EAED,MAAM4D,kBAAkB,GAAIhG,EAAE,IAAK;IACjCI,UAAU,CAACD,OAAO,CAACoD,MAAM,CAACkD,CAAC,IAAIA,CAAC,CAACzG,EAAE,KAAKA,EAAE,CAAC,CAAC;IAC5CM,WAAW,CAACD,QAAQ,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAKhD,EAAE,CAAC,CAAC;IACpDlC,OAAO,CAAC4I,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMvF,UAAU,CAACwF,cAAc,CAAC,CAAC;MAChDrG,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,aAAa,EAAE;QACjB;QACAT,UAAU,CAACD,OAAO,CAACgF,GAAG,CAACsB,CAAC,IACtBA,CAAC,CAACzG,EAAE,KAAKa,aAAa,CAACb,EAAE,GACrB;UAAE,GAAGyG,CAAC;UAAE,GAAGG,MAAM;UAAE9D,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC,GACtEN,CACN,CAAC,CAAC;QACF3I,OAAO,CAAC4I,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMM,SAAS,GAAG;UAChBhH,EAAE,EAAEuF,IAAI,CAAC0B,GAAG,CAAC,CAAC;UACd,GAAGL,MAAM;UACTpE,aAAa,EAAE,CAAC;UAChBC,iBAAiB,EAAE,CAAC;UACpBC,UAAU,EAAE,CAAC;UACbE,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,IAAI0C,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjDjE,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACD3G,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE6G,SAAS,CAAC,CAAC;QACnClJ,OAAO,CAAC4I,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEAhG,qBAAqB,CAAC,KAAK,CAAC;MAC5BW,UAAU,CAACgF,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACR1G,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4G,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMR,MAAM,GAAG,MAAMrF,WAAW,CAACsF,cAAc,CAAC,CAAC;MACjDrG,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM6G,UAAU,GAAG;QACjBrH,EAAE,EAAEuF,IAAI,CAAC0B,GAAG,CAAC,CAAC;QACd,GAAGL,MAAM;QACTzD,SAAS,EAAE,CAAC;QACZN,SAAS,EAAE,IAAI0C,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjDjE,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAClD,CAAC;MACDzG,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEgH,UAAU,CAAC,CAAC;MACtCvJ,OAAO,CAAC4I,OAAO,CAAC,QAAQ,CAAC;MAEzB9F,sBAAsB,CAAC,KAAK,CAAC;MAC7BW,WAAW,CAAC8E,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACR1G,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8G,iBAAiB,GAAItE,QAAQ,IAAK;IACtC,OAAO3C,QAAQ,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAKA,QAAQ,CAAC;EACtD,CAAC;EAED,oBACEzD,OAAA;IAAKgI,SAAS,EAAC,SAAS;IAAArD,QAAA,gBACtB3E,OAAA;MAAKgI,SAAS,EAAC,aAAa;MAAArD,QAAA,eAC1B3E,OAAA,CAACC,KAAK;QAACgI,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAArD,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGN/E,OAAA,CAACrB,GAAG;MAACuJ,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAzD,QAAA,gBAC3C3E,OAAA,CAACpB,GAAG;QAACyJ,IAAI,EAAE,CAAE;QAAA1D,QAAA,eACX3E,OAAA,CAACnC,IAAI;UAAA8G,QAAA,eACH3E,OAAA,CAACnB,SAAS;YACRgE,KAAK,EAAC,0BAAM;YACZiD,KAAK,EAAEhC,YAAa;YACpBwE,MAAM,eAAEtI,OAAA,CAACJ,cAAc;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/E,OAAA,CAACpB,GAAG;QAACyJ,IAAI,EAAE,CAAE;QAAA1D,QAAA,eACX3E,OAAA,CAACnC,IAAI;UAAA8G,QAAA,eACH3E,OAAA,CAACnB,SAAS;YACRgE,KAAK,EAAC,0BAAM;YACZiD,KAAK,EAAE7C,aAAc;YACrBqF,MAAM,eAAEtI,OAAA,CAACP,YAAY;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/E,OAAA,CAACpB,GAAG;QAACyJ,IAAI,EAAE,CAAE;QAAA1D,QAAA,eACX3E,OAAA,CAACnC,IAAI;UAAA8G,QAAA,eACH3E,OAAA,CAACnB,SAAS;YACRgE,KAAK,EAAC,gCAAO;YACbiD,KAAK,EAAE5C,iBAAkB;YACzBoF,MAAM,eAAEtI,OAAA,CAACL,mBAAmB;cAACwI,KAAK,EAAE;gBAAEhG,KAAK,EAAE;cAAU;YAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/E,OAAA,CAACpB,GAAG;QAACyJ,IAAI,EAAE,CAAE;QAAA1D,QAAA,eACX3E,OAAA,CAACnC,IAAI;UAAA8G,QAAA,eACH3E,OAAA,CAACnB,SAAS;YACRgE,KAAK,EAAC,oBAAK;YACXiD,KAAK,EAAE3C,UAAW;YAClBoF,MAAM,EAAC,QAAG;YACVD,MAAM,eAAEtI,OAAA,CAACZ,gBAAgB;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/E,OAAA,CAACnC,IAAI;MAAA8G,QAAA,gBACH3E,OAAA;QAAKgI,SAAS,EAAC,SAAS;QAAArD,QAAA,eACtB3E,OAAA;UAAKgI,SAAS,EAAC,cAAc;UAAArD,QAAA,eAC3B3E,OAAA,CAACjC,MAAM;YACLoH,IAAI,EAAC,SAAS;YACde,IAAI,eAAElG,OAAA,CAACb,YAAY;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBoB,OAAO,EAAEU,kBAAmB;YAAAlC,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/E,OAAA,CAAChC,KAAK;QACJwK,OAAO,EAAElE,aAAc;QACvBmE,UAAU,EAAE7H,OAAQ;QACpB8H,MAAM,EAAC,IAAI;QACX1H,OAAO,EAAEA,OAAQ;QACjB2H,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC,CAAE;QACFC,UAAU,EAAE;UACVC,iBAAiB,EAAGlC,MAAM,IAAK;YAC7B,MAAMmC,cAAc,GAAGnB,iBAAiB,CAAChB,MAAM,CAACtG,EAAE,CAAC;YACnD,oBACET,OAAA;cAAKmI,KAAK,EAAE;gBAAEgB,MAAM,EAAE;cAAE,CAAE;cAAAxE,QAAA,gBACxB3E,OAAA,CAAChB,OAAO;gBAACoK,WAAW,EAAC,MAAM;gBAAAzE,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC1C/E,OAAA,CAACjB,IAAI;gBACHmG,IAAI,EAAC,OAAO;gBACZuD,UAAU,EAAES,cAAe;gBAC3BG,UAAU,EAAGjF,OAAO,iBAClBpE,OAAA,CAACjB,IAAI,CAACuK,IAAI;kBACRC,OAAO,EAAE,cACPvJ,OAAA,CAACjC,MAAM;oBACLoH,IAAI,EAAC,MAAM;oBACXD,IAAI,EAAC,OAAO;oBACZiB,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,aAAaD,SAAS,YAAYqG,MAAM,CAACtG,EAAE,aAAa2D,OAAO,CAAC3D,EAAE,EAAE,CAAE;oBAAAkE,QAAA,EAC/F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/E,OAAA,CAACjC,MAAM;oBACLoH,IAAI,EAAC,MAAM;oBACXD,IAAI,EAAC,OAAO;oBACZiB,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,aAAaD,SAAS,YAAYqG,MAAM,CAACtG,EAAE,aAAa2D,OAAO,CAAC3D,EAAE,EAAE,CAAE;oBAAAkE,QAAA,EAC/F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,CACT;kBAAAJ,QAAA,eAEF3E,OAAA,CAACjB,IAAI,CAACuK,IAAI,CAACE,IAAI;oBACbC,MAAM,eAAEzJ,OAAA,CAACZ,gBAAgB;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC7BlC,KAAK,eACH7C,OAAA,CAAC/B,KAAK;sBAAA0G,QAAA,GACHP,OAAO,CAACvB,KAAK,eACd7C,OAAA,CAAC9B,GAAG;wBAACiE,KAAK,EAAEO,mBAAmB,CAAC0B,OAAO,CAACrB,MAAM,CAAC,CAACZ,KAAM;wBAAAwC,QAAA,EACnDjC,mBAAmB,CAAC0B,OAAO,CAACrB,MAAM,CAAC,CAACX;sBAAI;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACR;oBACD2E,WAAW,EAAE,OAAOtF,OAAO,CAACR,SAAS,UAAUQ,OAAO,CAACb,SAAS;kBAAG;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cACX;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDmE,cAAc,CAACnF,MAAM,KAAK,CAAC,iBAC1B/D,OAAA;gBAAKmI,KAAK,EAAE;kBAAEwB,SAAS,EAAE,QAAQ;kBAAEC,OAAO,EAAE,MAAM;kBAAEzH,KAAK,EAAE;gBAAO,CAAE;gBAAAwC,QAAA,EAAC;cAErE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAEV,CAAC;UACD8E,aAAa,EAAEA,CAAA,KAAM;QACvB;MAAE;QAAAjF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP/E,OAAA,CAAC7B,KAAK;MACJ0E,KAAK,EAAEvB,aAAa,GAAG,MAAM,GAAG,MAAO;MACvCwI,IAAI,EAAE5I,kBAAmB;MACzB6I,IAAI,EAAE3C,mBAAoB;MAC1B4C,QAAQ,EAAEA,CAAA,KAAM;QACd7I,qBAAqB,CAAC,KAAK,CAAC;QAC5BW,UAAU,CAACgF,WAAW,CAAC,CAAC;MAC1B,CAAE;MACFmD,cAAc,EAAEjJ,OAAQ;MACxBkJ,KAAK,EAAE,GAAI;MAAAvF,QAAA,eAEX3E,OAAA,CAAC5B,IAAI;QACH+L,IAAI,EAAErI,UAAW;QACjBsI,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UAAEtH,MAAM,EAAE;QAAW,CAAE;QAAA4B,QAAA,gBAEtC3E,OAAA,CAACrB,GAAG;UAACuJ,MAAM,EAAE,EAAG;UAAAvD,QAAA,gBACd3E,OAAA,CAACpB,GAAG;YAACyJ,IAAI,EAAE,EAAG;YAAA1D,QAAA,eACZ3E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;cACRgB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAElM,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAoG,QAAA,eAEhD3E,OAAA,CAAC3B,KAAK;gBAACqM,WAAW,EAAC;cAAS;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/E,OAAA,CAACpB,GAAG;YAACyJ,IAAI,EAAE,CAAE;YAAA1D,QAAA,eACX3E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;cACRgB,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAElM,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAoG,QAAA,eAE/C3E,OAAA,CAAC3B,KAAK;gBAAC8G,IAAI,EAAC,QAAQ;gBAACuF,WAAW,EAAC;cAAG;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;UACRgB,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoG,QAAA,eAEhD3E,OAAA,CAACG,QAAQ;YACPwK,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAmB;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/E,OAAA,CAACrB,GAAG;UAACuJ,MAAM,EAAE,EAAG;UAAAvD,QAAA,gBACd3E,OAAA,CAACpB,GAAG;YAACyJ,IAAI,EAAE,EAAG;YAAA1D,QAAA,eACZ3E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;cACRgB,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAElM,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAoG,QAAA,eAEhD3E,OAAA,CAAC1B,MAAM;gBAAAqG,QAAA,EACJe,MAAM,CAACC,OAAO,CAAC1D,kBAAkB,CAAC,CAAC2D,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,kBACpD7F,OAAA,CAACI,MAAM;kBAAW0F,KAAK,EAAEtB,GAAI;kBAAAG,QAAA,EAAEkB,MAAM,CAACzD;gBAAI,GAA7BoC,GAAG;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/E,OAAA,CAACpB,GAAG;YAACyJ,IAAI,EAAE,EAAG;YAAA1D,QAAA,eACZ3E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;cACRgB,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAC,0BAAM;cAAA5F,QAAA,eAEZ3E,OAAA,CAAC3B,KAAK;gBAAC8G,IAAI,EAAC,QAAQ;gBAACuF,WAAW,EAAC,OAAO;gBAACnC,MAAM,EAAC;cAAG;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;UACRgB,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,0BAAM;UAAA5F,QAAA,eAEZ3E,OAAA,CAACG,QAAQ;YACPwK,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAqB;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR/E,OAAA,CAAC7B,KAAK;MACJ0E,KAAK,EAAC,0BAAM;MACZiH,IAAI,EAAE1I,mBAAoB;MAC1B2I,IAAI,EAAElC,oBAAqB;MAC3BmC,QAAQ,EAAEA,CAAA,KAAM;QACd3I,sBAAsB,CAAC,KAAK,CAAC;QAC7BW,WAAW,CAAC8E,WAAW,CAAC,CAAC;MAC3B,CAAE;MACFmD,cAAc,EAAEjJ,OAAQ;MACxBkJ,KAAK,EAAE,GAAI;MAAAvF,QAAA,eAEX3E,OAAA,CAAC5B,IAAI;QACH+L,IAAI,EAAEnI,WAAY;QAClBoI,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UAAEtH,MAAM,EAAE;QAAW,CAAE;QAAA4B,QAAA,gBAEtC3E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;UACRgB,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoG,QAAA,eAEhD3E,OAAA,CAAC1B,MAAM;YAACoM,WAAW,EAAC,gCAAO;YAAA/F,QAAA,EACxB/D,OAAO,CAACgF,GAAG,CAACmB,MAAM,iBACjB/G,OAAA,CAACI,MAAM;cAAiB0F,KAAK,EAAEiB,MAAM,CAACtG,EAAG;cAAAkE,QAAA,EACtCoC,MAAM,CAAClE;YAAK,GADFkE,MAAM,CAACtG,EAAE;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ/E,OAAA,CAACrB,GAAG;UAACuJ,MAAM,EAAE,EAAG;UAAAvD,QAAA,gBACd3E,OAAA,CAACpB,GAAG;YAACyJ,IAAI,EAAE,EAAG;YAAA1D,QAAA,eACZ3E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;cACRgB,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAElM,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAoG,QAAA,eAEhD3E,OAAA,CAAC3B,KAAK;gBAACqM,WAAW,EAAC;cAAS;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/E,OAAA,CAACpB,GAAG;YAACyJ,IAAI,EAAE,CAAE;YAAA1D,QAAA,eACX3E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;cACRgB,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAElM,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAoG,QAAA,eAEhD3E,OAAA,CAAC3B,KAAK;gBAAC8G,IAAI,EAAC,QAAQ;gBAACuF,WAAW,EAAC;cAAG;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;UACRgB,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoG,QAAA,eAEhD3E,OAAA,CAAC1B,MAAM;YAAAqG,QAAA,EACJe,MAAM,CAACC,OAAO,CAACjD,mBAAmB,CAAC,CAACkD,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,kBACrD7F,OAAA,CAACI,MAAM;cAAW0F,KAAK,EAAEtB,GAAI;cAAAG,QAAA,EAAEkB,MAAM,CAACzD;YAAI,GAA7BoC,GAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ/E,OAAA,CAAC5B,IAAI,CAACkL,IAAI;UACRgB,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,0BAAM;UAAA5F,QAAA,eAEZ3E,OAAA,CAACG,QAAQ;YACPwK,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAoB;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvE,EAAA,CAzoBID,UAAU;EAAA,QACY5C,SAAS,EAClBC,WAAW,EAUPQ,IAAI,CAAC2D,OAAO,EACX3D,IAAI,CAAC2D,OAAO;AAAA;AAAA6I,EAAA,GAb9BrK,UAAU;AA2oBhB,eAAeA,UAAU;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}